import { relations } from 'drizzle-orm';
import {
  boolean,
  datetime,
  decimal,
  int,
  json,
  mysqlEnum,
  mysqlTable,
  primaryKey,
  text,
  timestamp,
  unique,
  varchar,
} from 'drizzle-orm/mysql-core';

// Admin Roles table
export const adminRoles = mysqlTable('admin_roles', {
  id: varchar('id', { length: 255 }).primaryKey(),
  name: varchar('name', { length: 100 }).notNull().unique(),
  description: text('description'),
  isSystem: boolean('is_system').default(false),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().onUpdateNow().notNull(),
});

// Admin Roles relations
export const adminRolesRelations = relations(adminRoles, ({ many }) => ({
  permissions: many(adminRolePermissions),
  users: many(users),
}));

// Admin Permissions table
export const adminPermissions = mysqlTable('admin_permissions', {
  id: varchar('id', { length: 255 }).primary<PERSON>ey(),
  name: varchar('name', { length: 100 }).notNull().unique(),
  code: varchar('code', { length: 100 }).notNull().unique(),
  description: text('description'),
  module: varchar('module', { length: 100 }).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
});

// Admin Permissions relations
export const adminPermissionsRelations = relations(adminPermissions, ({ many }) => ({
  roles: many(adminRolePermissions),
}));

// Admin Role Permissions table
export const adminRolePermissions = mysqlTable('admin_role_permissions', {
  id: varchar('id', { length: 255 }).primaryKey(),
  roleId: varchar('role_id', { length: 255 }).notNull(),
  permissionId: varchar('permission_id', { length: 255 }).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
});

// Admin Role Permissions relations
export const adminRolePermissionsRelations = relations(adminRolePermissions, ({ one }) => ({
  role: one(adminRoles, {
    fields: [adminRolePermissions.roleId],
    references: [adminRoles.id],
  }),
  permission: one(adminPermissions, {
    fields: [adminRolePermissions.permissionId],
    references: [adminPermissions.id],
  }),
}));

// Site Settings table
export const siteSettings = mysqlTable('site_settings', {
  id: varchar('id', { length: 255 }).primaryKey(),
  key: varchar('setting_key', { length: 100 }).notNull().unique(),
  value: text('value'),
  type: varchar('type', { length: 50 }).notNull(),
  groupName: varchar('group_name', { length: 100 }).notNull(),
  label: varchar('label', { length: 255 }).notNull(),
  description: text('description'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().onUpdateNow().notNull(),
});

// Users table
export const users = mysqlTable('users', {
  id: varchar('id', { length: 255 }).primaryKey(),
  name: varchar('name', { length: 255 }),
  username: varchar('username', { length: 50 }).unique(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  password: varchar('password', { length: 255 }), // Password field for authentication
  phone: varchar('phone', { length: 20 }),
  role: varchar('role', { length: 50 }).default('user'),
  isAdmin: boolean('is_admin').default(false),
  adminRoleId: varchar('admin_role_id', { length: 255 }),
  emailVerified: timestamp('emailVerified', { mode: 'date' }),
  isVerified: boolean('is_verified').default(false), // User verification status
  image: varchar('image', { length: 255 }),
  coverImage: varchar('coverImage', { length: 255 }),
  bio: text('bio'),
  location: varchar('location', { length: 255 }),
  birthday: datetime('birthday'),
  // Work & Education
  work: varchar('work', { length: 255 }),
  education: varchar('education', { length: 255 }),
  // Social links
  website: varchar('website', { length: 255 }),
  facebook: varchar('facebook', { length: 255 }),
  twitter: varchar('twitter', { length: 255 }),
  instagram: varchar('instagram', { length: 255 }),
  linkedin: varchar('linkedin', { length: 255 }),
  youtube: varchar('youtube', { length: 255 }),
  // Privacy settings
  profileVisibility: mysqlEnum('profile_visibility', ['public', 'subscribers', 'private']).default('public'),
  showEmail: boolean('show_email').default(false),
  showPhone: boolean('show_phone').default(false),
  showBirthday: boolean('show_birthday').default(true),
  showLocation: boolean('show_location').default(true),
  allowFriendRequests: mysqlEnum('allow_friend_requests', ['everyone', 'friends-of-friends', 'nobody']).default('everyone'),
  defaultPostPrivacy: mysqlEnum('default_post_privacy', ['public', 'subscribers', 'private']).default('public'),
  allowTagging: boolean('allow_tagging').default(true),
  allowMessagesFrom: mysqlEnum('allow_messages_from', ['everyone', 'subscribers', 'nobody']).default('everyone'),
  showOnlineStatus: boolean('show_online_status').default(true),
  allowSearchByEmail: boolean('allow_search_by_email').default(true),
  allowSearchByPhone: boolean('allow_search_by_phone').default(false),
  // Account status fields
  status: mysqlEnum('status', ['active', 'disabled', 'suspended', 'deleted']).default('active').notNull(),
  isActive: boolean('is_active').default(true).notNull(),
  suspendedAt: timestamp('suspended_at'),
  suspendedReason: text('suspended_reason'),
  suspendedBy: varchar('suspended_by', { length: 255 }),
  deletedAt: timestamp('deleted_at'),
  deletedReason: text('deleted_reason'),
  deletedBy: varchar('deleted_by', { length: 255 }),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// User relations
export const usersRelations = relations(users, ({ one, many }) => ({
  adminRole: one(adminRoles, {
    fields: [users.adminRoleId],
    references: [adminRoles.id],
  }),
  posts: many(posts),
  comments: many(comments),
  likes: many(likes),
  groups: many(groups, { relationName: 'creator' }),
  groupMemberships: many(groupMembers),
  groupReportsMade: many(groupReports, { relationName: 'reporter' }),
  groupReportsReceived: many(groupReports, { relationName: 'reportedUser' }),
  subscriptions: many(subscriptions, { relationName: 'subscriber' }),
  subscribers: many(subscriptions, { relationName: 'targetUser' }),
  sentMessages: many(messages, { relationName: 'sender' }),
  receivedMessages: many(messages, { relationName: 'receiver' }),
  notifications: many(notifications, { relationName: 'recipient' }),
  reportsMade: many(reports, { relationName: 'reporter' }),
  reportsReceived: many(reports, { relationName: 'reportedUser' }),
  savedPosts: many(savedPosts),
  postReportsMade: many(postReports, { relationName: 'reporter' }),
  postReportsReviewed: many(postReports, { relationName: 'reviewer' }),
  hostedEvents: many(events, { relationName: 'host' }),
  eventAttendances: many(eventAttendees),
  eventInvitesSent: many(eventInvites, { relationName: 'sender' }),
  eventInvitesReceived: many(eventInvites, { relationName: 'recipient' }),
  eventComments: many(eventComments),
  stores: many(stores),
  storeFollows: many(storeFollows),
  storeReviews: many(storeReviews),
  products: many(products),
  productReports: many(productReports),
  fanPages: many(fanPages),
  fanPageFollows: many(fanPageFollowers),
  fanPageRoles: many(fanPageRoles),
  fanPagePostComments: many(fanPagePostComments),
  fanPagePostLikes: many(fanPagePostLikes),
  referralCode: one(referralCodes),
  referralsMade: many(referrals, { relationName: 'referrer' }),
  referralsReceived: many(referrals, { relationName: 'referred' }),
}));

// Posts table
export const posts = mysqlTable('posts', {
  id: varchar('id', { length: 255 }).primaryKey(),
  userId: varchar('userId', { length: 255 }).notNull(),
  groupId: varchar('groupId', { length: 255 }),
  content: text('content'),
  images: json('images').$type<string[]>(),
  videos: json('videos').$type<string[]>(),
  privacy: mysqlEnum('privacy', ['public', 'subscribers', 'private']).default('public').notNull(),
  sharedPostId: varchar('sharedPostId', { length: 255 }),
  backgroundColor: varchar('backgroundColor', { length: 50 }),
  feeling: varchar('feeling', { length: 100 }),
  activity: varchar('activity', { length: 100 }),
  location: varchar('location', { length: 255 }),
  formattedContent: boolean('formattedContent').default(false),
  isReported: boolean('isReported').default(false),
  moderationStatus: mysqlEnum('moderationStatus', ['pending', 'approved', 'rejected']).default('approved').notNull(),
  moderatedBy: varchar('moderatedBy', { length: 255 }),
  moderatedAt: timestamp('moderatedAt'),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});



// Post relations
export const postsRelations = relations(posts, ({ one, many }) => ({
  user: one(users, {
    fields: [posts.userId],
    references: [users.id],
  }),
  group: one(groups, {
    fields: [posts.groupId],
    references: [groups.id],
  }),
  comments: many(comments),
  likes: many(likes),
  sharedPost: one(posts, {
    fields: [posts.sharedPostId],
    references: [posts.id],
    relationName: 'sharedPost',
  }),
  shares: many(posts, { relationName: 'sharedPost' }),
  savedBy: many(savedPosts),
  reports: many(postReports),
}));

// Comments table
export const comments = mysqlTable('comments', {
  id: varchar('id', { length: 255 }).primaryKey(),
  content: text('content').notNull(),
  userId: varchar('userId', { length: 255 }).notNull(),
  postId: varchar('postId', { length: 255 }).notNull(),
  parentId: varchar('parentId', { length: 255 }),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// Comment relations
export const commentsRelations = relations(comments, ({ one, many }) => ({
  user: one(users, {
    fields: [comments.userId],
    references: [users.id],
  }),
  post: one(posts, {
    fields: [comments.postId],
    references: [posts.id],
  }),
  parent: one(comments, {
    fields: [comments.parentId],
    references: [comments.id],
  }),
  replies: many(comments, { relationName: 'parent' }),
  likes: many(likes),
}));

// Likes table
export const likes = mysqlTable('likes', {
  id: varchar('id', { length: 255 }).primaryKey(),
  userId: varchar('userId', { length: 255 }).notNull(),
  postId: varchar('postId', { length: 255 }),
  commentId: varchar('commentId', { length: 255 }),
  type: mysqlEnum('type', ['like', 'dislike']).default('like').notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
});

// Like relations
export const likesRelations = relations(likes, ({ one }) => ({
  user: one(users, {
    fields: [likes.userId],
    references: [users.id],
  }),
  post: one(posts, {
    fields: [likes.postId],
    references: [posts.id],
  }),
  comment: one(comments, {
    fields: [likes.commentId],
    references: [comments.id],
  }),
}));



// Subscriptions table
export const subscriptions = mysqlTable('subscriptions', {
  id: varchar('id', { length: 255 }).primaryKey(),
  subscriberId: varchar('subscriberId', { length: 255 }).notNull(),
  targetUserId: varchar('targetUserId', { length: 255 }).notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
}, (table) => ({
  // Unique constraint to prevent duplicate subscriptions
  uniqueSubscription: unique().on(table.subscriberId, table.targetUserId),
}));

// Subscription relations
export const subscriptionsRelations = relations(subscriptions, ({ one }) => ({
  subscriber: one(users, {
    fields: [subscriptions.subscriberId],
    references: [users.id],
    relationName: 'subscriber',
  }),
  targetUser: one(users, {
    fields: [subscriptions.targetUserId],
    references: [users.id],
    relationName: 'targetUser',
  }),
}));

// Subscription Plans table
export const subscriptionPlans = mysqlTable('subscription_plans', {
  id: varchar('id', { length: 255 }).primaryKey(),
  name: varchar('name', { length: 100 }).notNull(),
  displayName: varchar('displayName', { length: 100 }).notNull(),
  description: text('description'),
  price: decimal('price', { precision: 10, scale: 2 }).notNull(),
  currency: varchar('currency', { length: 10 }).default('USD').notNull(),
  billingCycle: mysqlEnum('billingCycle', ['monthly', 'yearly']).default('monthly').notNull(),
  // Custom billing cycle fields
  customBillingMonths: int('customBillingMonths').default(0), // Custom months for billing cycle (0 = use default)
  customBillingYears: int('customBillingYears').default(0), // Custom years for billing cycle (0 = use default)
  features: json('features').$type<string[]>(),
  maxPosts: int('maxPosts').default(-1), // -1 for unlimited
  maxStorage: int('maxStorage').default(-1), // in MB, -1 for unlimited
  maxGroups: int('maxGroups').default(-1), // -1 for unlimited
  canCreateFanPages: boolean('canCreateFanPages').default(false),
  canCreateStores: boolean('canCreateStores').default(false),
  canMonetizeBlogs: boolean('canMonetizeBlogs').default(false),
  prioritySupport: boolean('prioritySupport').default(false),
  // Verification system
  grantsVerification: boolean('grantsVerification').default(false), // Whether this plan grants user verification
  // Badge system fields
  badgeType: mysqlEnum('badgeType', ['none', 'crown', 'star', 'diamond', 'vip', 'custom']).default('none').notNull(),
  badgeColor: varchar('badgeColor', { length: 7 }).default('#3B82F6'), // Hex color for ready-made badges
  customBadgeUrl: varchar('customBadgeUrl', { length: 500 }), // URL for custom uploaded badge
  badgePriority: int('badgePriority').default(0), // Higher number = higher priority for display
  isActive: boolean('isActive').default(true).notNull(),
  sortOrder: int('sortOrder').default(0),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// User Subscriptions table
export const userSubscriptions = mysqlTable('user_subscriptions', {
  id: varchar('id', { length: 255 }).primaryKey(),
  userId: varchar('userId', { length: 255 }).notNull(),
  planId: varchar('planId', { length: 255 }).notNull(),
  status: mysqlEnum('status', ['active', 'cancelled', 'expired', 'pending', 'suspended']).default('pending').notNull(),
  startDate: timestamp('startDate').notNull(),
  endDate: timestamp('endDate').notNull(),
  nextBillingDate: timestamp('nextBillingDate'),
  cancelledAt: timestamp('cancelledAt'),
  cancelReason: text('cancelReason'),
  autoRenew: boolean('autoRenew').default(true).notNull(),
  paymentMethod: varchar('paymentMethod', { length: 50 }),
  lastPaymentDate: timestamp('lastPaymentDate'),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// Subscription Transactions table
export const subscriptionTransactions = mysqlTable('subscription_transactions', {
  id: varchar('id', { length: 255 }).primaryKey(),
  subscriptionId: varchar('subscriptionId', { length: 255 }).notNull(),
  userId: varchar('userId', { length: 255 }).notNull(),
  planId: varchar('planId', { length: 255 }).notNull(),
  type: mysqlEnum('type', ['payment', 'refund', 'upgrade', 'downgrade', 'cancellation']).notNull(),
  amount: decimal('amount', { precision: 10, scale: 2 }).notNull(),
  currency: varchar('currency', { length: 10 }).default('USD').notNull(),
  status: mysqlEnum('status', ['pending', 'completed', 'failed', 'cancelled']).default('pending').notNull(),
  paymentGateway: varchar('paymentGateway', { length: 50 }),
  gatewayTransactionId: varchar('gatewayTransactionId', { length: 255 }),
  gatewayResponse: json('gatewayResponse'),
  description: text('description'),
  metadata: json('metadata'),
  processedAt: timestamp('processedAt'),

  // Referral Tracking
  referralId: varchar('referralId', { length: 255 }), // Links to referrals table if user was referred
  referrerId: varchar('referrerId', { length: 255 }), // Who referred this user
  isFirstPurchase: boolean('isFirstPurchase').default(false).notNull(),
  commissionProcessed: boolean('commissionProcessed').default(false).notNull(),

  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// Subscription Plan Relations
export const subscriptionPlansRelations = relations(subscriptionPlans, ({ many }) => ({
  userSubscriptions: many(userSubscriptions),
  transactions: many(subscriptionTransactions),
}));

// User Subscription Relations
export const userSubscriptionsRelations = relations(userSubscriptions, ({ one, many }) => ({
  user: one(users, {
    fields: [userSubscriptions.userId],
    references: [users.id],
  }),
  plan: one(subscriptionPlans, {
    fields: [userSubscriptions.planId],
    references: [subscriptionPlans.id],
  }),
  transactions: many(subscriptionTransactions),
}));

// Subscription Transaction Relations
export const subscriptionTransactionsRelations = relations(subscriptionTransactions, ({ one }) => ({
  subscription: one(userSubscriptions, {
    fields: [subscriptionTransactions.subscriptionId],
    references: [userSubscriptions.id],
  }),
  user: one(users, {
    fields: [subscriptionTransactions.userId],
    references: [users.id],
  }),
  plan: one(subscriptionPlans, {
    fields: [subscriptionTransactions.planId],
    references: [subscriptionPlans.id],
  }),
}));

// Messages table
export const messages = mysqlTable('messages', {
  id: varchar('id', { length: 255 }).primaryKey(),
  senderId: varchar('senderId', { length: 255 }).notNull(),
  receiverId: varchar('receiverId', { length: 255 }).notNull(),
  content: text('content').notNull(),
  read: boolean('read').default(false).notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
});

// Message relations
export const messagesRelations = relations(messages, ({ one }) => ({
  sender: one(users, {
    fields: [messages.senderId],
    references: [users.id],
    relationName: 'sender',
  }),
  receiver: one(users, {
    fields: [messages.receiverId],
    references: [users.id],
    relationName: 'receiver',
  }),
}));

// Fan Page Messages table
export const fanPageMessages = mysqlTable('fan_page_messages', {
  id: varchar('id', { length: 255 }).primaryKey(),
  fanPageId: varchar('fanPageId', { length: 255 }).notNull(),
  senderId: varchar('senderId', { length: 255 }).notNull(),
  content: text('content').notNull(),
  isFromPage: boolean('isFromPage').default(false).notNull(), // true if message is from page owner, false if from user
  read: boolean('read').default(false).notNull(),
  senderType: mysqlEnum('senderType', ['user', 'page']).notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
});

// Fan Page Message relations
export const fanPageMessagesRelations = relations(fanPageMessages, ({ one }) => ({
  fanPage: one(fanPages, {
    fields: [fanPageMessages.fanPageId],
    references: [fanPages.id],
  }),
  sender: one(users, {
    fields: [fanPageMessages.senderId],
    references: [users.id],
  }),
}));

// Notifications table
export const notifications = mysqlTable('notifications', {
  id: varchar('id', { length: 255 }).primaryKey(),
  recipientId: varchar('recipientId', { length: 255 }).notNull(),
  type: mysqlEnum('type', [
    'like', 'comment', 'message',
    'group_invite', 'group_join_request', 'group_join_approved', 'group_post', 'group_announcement',
    'event_invite', 'event_reminder', 'event_update', 'event_comment',
    'store_follow', 'store_review', 'product_new', 'product_report',
    'fan_page_follow', 'fan_page_post', 'fan_page_comment', 'fan_page_like', 'fan_page_role_added',
    'subscription', 'subscription_back'
  ]).notNull(),
  senderId: varchar('senderId', { length: 255 }),
  postId: varchar('postId', { length: 255 }),
  commentId: varchar('commentId', { length: 255 }),
  messageId: varchar('messageId', { length: 255 }),

  subscriptionId: varchar('subscriptionId', { length: 255 }),
  groupId: varchar('groupId', { length: 255 }),
  eventId: varchar('eventId', { length: 255 }),
  storeId: varchar('storeId', { length: 255 }),
  productId: varchar('productId', { length: 255 }),
  fanPageId: varchar('fanPageId', { length: 255 }),
  fanPagePostId: varchar('fanPagePostId', { length: 255 }),
  read: boolean('read').default(false).notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
});

// User Reports table
export const reports = mysqlTable('reports', {
  id: varchar('id', { length: 255 }).primaryKey(),
  reporterId: varchar('reporterId', { length: 255 }).notNull(),
  reportedUserId: varchar('reportedUserId', { length: 255 }).notNull(),
  reason: mysqlEnum('reason', ['spam', 'harassment', 'inappropriate_content', 'impersonation', 'other']).notNull(),
  description: text('description'),
  status: mysqlEnum('status', ['pending', 'reviewed', 'resolved', 'dismissed']).default('pending').notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// Report relations
export const reportsRelations = relations(reports, ({ one }) => ({
  reporter: one(users, {
    fields: [reports.reporterId],
    references: [users.id],
    relationName: 'reporter',
  }),
  reportedUser: one(users, {
    fields: [reports.reportedUserId],
    references: [users.id],
    relationName: 'reportedUser',
  }),
}));

// Notification relations
export const notificationsRelations = relations(notifications, ({ one }) => ({
  recipient: one(users, {
    fields: [notifications.recipientId],
    references: [users.id],
    relationName: 'recipient',
  }),
  sender: one(users, {
    fields: [notifications.senderId],
    references: [users.id],
  }),
  post: one(posts, {
    fields: [notifications.postId],
    references: [posts.id],
  }),
  comment: one(comments, {
    fields: [notifications.commentId],
    references: [comments.id],
  }),
  message: one(messages, {
    fields: [notifications.messageId],
    references: [messages.id],
  }),
  subscription: one(subscriptions, {
    fields: [notifications.subscriptionId],
    references: [subscriptions.id],
  }),
  group: one(groups, {
    fields: [notifications.groupId],
    references: [groups.id],
  }),
  event: one(events, {
    fields: [notifications.eventId],
    references: [events.id],
  }),
  store: one(stores, {
    fields: [notifications.storeId],
    references: [stores.id],
  }),
  product: one(products, {
    fields: [notifications.productId],
    references: [products.id],
  }),
  fanPage: one(fanPages, {
    fields: [notifications.fanPageId],
    references: [fanPages.id],
  }),
  fanPagePost: one(fanPagePosts, {
    fields: [notifications.fanPagePostId],
    references: [fanPagePosts.id],
  }),
}));

// Auth tables for NextAuth
export const accounts = mysqlTable('accounts', {
  id: varchar('id', { length: 255 }).primaryKey(),
  userId: varchar('userId', { length: 255 }).notNull(),
  type: varchar('type', { length: 255 }).notNull(),
  provider: varchar('provider', { length: 255 }).notNull(),
  providerAccountId: varchar('providerAccountId', { length: 255 }).notNull(),
  refresh_token: text('refresh_token'),
  access_token: text('access_token'),
  expires_at: int('expires_at'),
  token_type: varchar('token_type', { length: 255 }),
  scope: varchar('scope', { length: 255 }),
  id_token: text('id_token'),
  session_state: varchar('session_state', { length: 255 }),
});

export const accountsRelations = relations(accounts, ({ one }) => ({
  user: one(users, {
    fields: [accounts.userId],
    references: [users.id],
  }),
}));

export const sessions = mysqlTable('sessions', {
  id: varchar('id', { length: 255 }).primaryKey(),
  sessionToken: varchar('sessionToken', { length: 255 }).notNull().unique(),
  userId: varchar('userId', { length: 255 }).notNull(),
  expires: timestamp('expires').notNull(),
});

export const sessionsRelations = relations(sessions, ({ one }) => ({
  user: one(users, {
    fields: [sessions.userId],
    references: [users.id],
  }),
}));

export const verificationTokens = mysqlTable(
  'verificationTokens',
  {
    identifier: varchar('identifier', { length: 255 }).notNull(),
    token: varchar('token', { length: 255 }).notNull(),
    expires: timestamp('expires').notNull(),
  },
  (vt) => ({
    compoundKey: primaryKey(vt.identifier, vt.token),
  })
);



// Saved Posts table
export const savedPosts = mysqlTable('savedPosts', {
  id: varchar('id', { length: 255 }).primaryKey(),
  userId: varchar('userId', { length: 255 }).notNull(),
  postId: varchar('postId', { length: 255 }).notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
});

// Saved Posts relations
export const savedPostsRelations = relations(savedPosts, ({ one }) => ({
  user: one(users, {
    fields: [savedPosts.userId],
    references: [users.id],
  }),
  post: one(posts, {
    fields: [savedPosts.postId],
    references: [posts.id],
  }),
}));

// Post Reports table
export const postReports = mysqlTable('post_reports', {
  id: varchar('id', { length: 255 }).primaryKey(),
  postId: varchar('postId', { length: 255 }).notNull(),
  reporterId: varchar('reporterId', { length: 255 }).notNull(),
  reason: mysqlEnum('reason', ['spam', 'harassment', 'inappropriate_content', 'violence', 'misinformation', 'other']).notNull(),
  description: text('description'),
  status: mysqlEnum('status', ['pending', 'reviewed', 'resolved', 'dismissed']).default('pending').notNull(),
  reviewedBy: varchar('reviewedBy', { length: 255 }),
  reviewedAt: timestamp('reviewedAt'),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// Post Reports relations
export const postReportsRelations = relations(postReports, ({ one }) => ({
  post: one(posts, {
    fields: [postReports.postId],
    references: [posts.id],
  }),
  reporter: one(users, {
    fields: [postReports.reporterId],
    references: [users.id],
    relationName: 'reporter',
  }),
  reviewer: one(users, {
    fields: [postReports.reviewedBy],
    references: [users.id],
    relationName: 'reviewer',
  }),
}));

// Blogs table
export const blogs = mysqlTable('blogs', {
  id: varchar('id', { length: 255 }).primaryKey(),
  title: varchar('title', { length: 500 }).notNull(),
  slug: varchar('slug', { length: 500 }).unique().notNull(),
  excerpt: text('excerpt'),
  content: text('content').notNull(),
  coverImage: varchar('coverImage', { length: 255 }),
  authorId: varchar('authorId', { length: 255 }).notNull(),
  categoryId: varchar('categoryId', { length: 255 }),
  tags: json('tags').$type<string[]>(),
  status: mysqlEnum('status', ['draft', 'published', 'archived']).default('draft').notNull(),
  readTime: int('readTime'), // in minutes
  viewCount: int('viewCount').default(0),
  featured: boolean('featured').default(false),
  seoTitle: varchar('seoTitle', { length: 255 }),
  seoDescription: text('seoDescription'),
  publishedAt: timestamp('publishedAt'),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// Blog Monetization Settings table
export const blogMonetization = mysqlTable('blog_monetization', {
  id: varchar('id', { length: 255 }).primaryKey(),
  blogId: varchar('blogId', { length: 255 }).notNull(),
  isEnabled: boolean('isEnabled').default(false),
  isApproved: boolean('isApproved').default(false),
  approvedAt: timestamp('approvedAt'),
  approvedBy: varchar('approvedBy', { length: 255 }),
  rejectedAt: timestamp('rejectedAt'),
  rejectedBy: varchar('rejectedBy', { length: 255 }),
  rejectionReason: text('rejectionReason'),
  cprRate: decimal('cprRate', { precision: 10, scale: 4 }).default('0.0000'),
  totalReads: int('totalReads').default(0),
  uniqueReads: int('uniqueReads').default(0),
  totalEarnings: decimal('totalEarnings', { precision: 15, scale: 2 }).default('0.00'),
  lastPayoutAt: timestamp('lastPayoutAt'),
  status: mysqlEnum('status', ['pending', 'approved', 'rejected', 'suspended']).default('pending'),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// Blog Views table - Enhanced view tracking
export const blogViews = mysqlTable('blog_views', {
  id: varchar('id', { length: 255 }).primaryKey(),
  blogId: varchar('blogId', { length: 255 }).notNull(),
  userId: varchar('userId', { length: 255 }),
  ipAddress: varchar('ipAddress', { length: 45 }).notNull(),
  userAgent: text('userAgent'),
  sessionId: varchar('sessionId', { length: 255 }).notNull(),
  fingerprint: varchar('fingerprint', { length: 255 }), // Browser fingerprint
  referrer: varchar('referrer', { length: 500 }),
  country: varchar('country', { length: 100 }),
  city: varchar('city', { length: 100 }),
  device: varchar('device', { length: 50 }), // mobile, desktop, tablet
  browser: varchar('browser', { length: 50 }),
  os: varchar('os', { length: 50 }),
  isBot: boolean('isBot').default(false),
  isUnique: boolean('isUnique').default(true),
  viewDuration: int('viewDuration').default(0), // in seconds
  scrollDepth: int('scrollDepth').default(0), // percentage
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow(),
});

// Blog Read Tracking table - Enhanced for monetization
export const blogReads = mysqlTable('blog_reads', {
  id: varchar('id', { length: 255 }).primaryKey(),
  blogId: varchar('blogId', { length: 255 }).notNull(),
  userId: varchar('userId', { length: 255 }),
  ipAddress: varchar('ipAddress', { length: 45 }).notNull(),
  userAgent: text('userAgent'),
  readDuration: int('readDuration').default(0), // in seconds
  isUnique: boolean('isUnique').default(true),
  isQualified: boolean('isQualified').default(false), // meets minimum read time
  sessionId: varchar('sessionId', { length: 255 }),
  referrer: varchar('referrer', { length: 500 }),
  scrollDepth: int('scrollDepth').default(0), // percentage
  engagementScore: decimal('engagementScore', { precision: 5, scale: 2 }).default('0.00'),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
});

// Blog Earnings table
export const blogEarnings = mysqlTable('blog_earnings', {
  id: varchar('id', { length: 255 }).primaryKey(),
  blogId: varchar('blogId', { length: 255 }).notNull(),
  authorId: varchar('authorId', { length: 255 }).notNull(),
  readCount: int('readCount').notNull(),
  qualifiedReads: int('qualifiedReads').notNull(),
  cprRate: decimal('cprRate', { precision: 10, scale: 4 }).notNull(),
  earningAmount: decimal('earningAmount', { precision: 15, scale: 2 }).notNull(),
  status: mysqlEnum('status', ['pending', 'paid', 'cancelled']).default('pending'),
  paidAt: timestamp('paidAt'),
  transactionId: varchar('transactionId', { length: 255 }),
  periodStart: timestamp('periodStart').notNull(),
  periodEnd: timestamp('periodEnd').notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
});

// Monetization Settings table (Global settings)
export const monetizationSettings = mysqlTable('monetization_settings', {
  id: varchar('id', { length: 255 }).primaryKey(),
  settingKey: varchar('settingKey', { length: 100 }).unique().notNull(),
  settingValue: text('settingValue').notNull(),
  dataType: mysqlEnum('dataType', ['string', 'number', 'boolean', 'json']).default('string'),
  description: text('description'),
  isActive: boolean('isActive').default(true),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// Blog relations
export const blogsRelations = relations(blogs, ({ one, many }) => ({
  author: one(users, {
    fields: [blogs.authorId],
    references: [users.id],
  }),
  category: one(blogCategories, {
    fields: [blogs.categoryId],
    references: [blogCategories.id],
  }),
  comments: many(blogComments),
  likes: many(blogLikes),
  dislikes: many(blogDislikes),
  bookmarks: many(blogBookmarks),
  monetization: one(blogMonetization, {
    fields: [blogs.id],
    references: [blogMonetization.blogId],
  }),
  reads: many(blogReads),
  earnings: many(blogEarnings),
}));

// Blog Monetization relations
export const blogMonetizationRelations = relations(blogMonetization, ({ one }) => ({
  blog: one(blogs, {
    fields: [blogMonetization.blogId],
    references: [blogs.id],
  }),
  approver: one(users, {
    fields: [blogMonetization.approvedBy],
    references: [users.id],
  }),
  rejector: one(users, {
    fields: [blogMonetization.rejectedBy],
    references: [users.id],
  }),
}));

// Blog Reads relations
export const blogReadsRelations = relations(blogReads, ({ one }) => ({
  blog: one(blogs, {
    fields: [blogReads.blogId],
    references: [blogs.id],
  }),
  user: one(users, {
    fields: [blogReads.userId],
    references: [users.id],
  }),
}));

// Blog Earnings relations
export const blogEarningsRelations = relations(blogEarnings, ({ one }) => ({
  blog: one(blogs, {
    fields: [blogEarnings.blogId],
    references: [blogs.id],
  }),
  author: one(users, {
    fields: [blogEarnings.authorId],
    references: [users.id],
  }),
}));

// Blog Categories table
export const blogCategories = mysqlTable('blogCategories', {
  id: varchar('id', { length: 255 }).primaryKey(),
  name: varchar('name', { length: 100 }).notNull(),
  slug: varchar('slug', { length: 100 }).unique().notNull(),
  description: text('description'),
  color: varchar('color', { length: 7 }).default('#3b82f6'), // hex color
  createdAt: timestamp('createdAt').defaultNow().notNull(),
});

// Blog Categories relations
export const blogCategoriesRelations = relations(blogCategories, ({ many }) => ({
  blogs: many(blogs),
}));

// Blog Comments table
export const blogComments = mysqlTable('blogComments', {
  id: varchar('id', { length: 255 }).primaryKey(),
  blogId: varchar('blogId', { length: 255 }).notNull(),
  userId: varchar('userId', { length: 255 }).notNull(),
  content: text('content').notNull(),
  parentId: varchar('parentId', { length: 255 }), // for nested comments
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// Blog Comments relations
export const blogCommentsRelations = relations(blogComments, ({ one, many }) => ({
  blog: one(blogs, {
    fields: [blogComments.blogId],
    references: [blogs.id],
  }),
  user: one(users, {
    fields: [blogComments.userId],
    references: [users.id],
  }),
  parent: one(blogComments, {
    fields: [blogComments.parentId],
    references: [blogComments.id],
    relationName: 'parentComment',
  }),
  replies: many(blogComments, { relationName: 'parentComment' }),
}));

// Blog Likes table
export const blogLikes = mysqlTable('blogLikes', {
  id: varchar('id', { length: 255 }).primaryKey(),
  blogId: varchar('blogId', { length: 255 }).notNull(),
  userId: varchar('userId', { length: 255 }).notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
});

// Blog Likes relations
export const blogLikesRelations = relations(blogLikes, ({ one }) => ({
  blog: one(blogs, {
    fields: [blogLikes.blogId],
    references: [blogs.id],
  }),
  user: one(users, {
    fields: [blogLikes.userId],
    references: [users.id],
  }),
}));

// Blog Dislikes table
export const blogDislikes = mysqlTable('blogDislikes', {
  id: varchar('id', { length: 255 }).primaryKey(),
  blogId: varchar('blogId', { length: 255 }).notNull(),
  userId: varchar('userId', { length: 255 }).notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
});

// Blog Dislikes relations
export const blogDislikesRelations = relations(blogDislikes, ({ one }) => ({
  blog: one(blogs, {
    fields: [blogDislikes.blogId],
    references: [blogs.id],
  }),
  user: one(users, {
    fields: [blogDislikes.userId],
    references: [users.id],
  }),
}));

// Blog Bookmarks table
export const blogBookmarks = mysqlTable('blogBookmarks', {
  id: varchar('id', { length: 255 }).primaryKey(),
  blogId: varchar('blogId', { length: 255 }).notNull(),
  userId: varchar('userId', { length: 255 }).notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
});

// Blog Bookmarks relations
export const blogBookmarksRelations = relations(blogBookmarks, ({ one }) => ({
  blog: one(blogs, {
    fields: [blogBookmarks.blogId],
    references: [blogs.id],
  }),
  user: one(users, {
    fields: [blogBookmarks.userId],
    references: [users.id],
  }),
}));

// Groups table
export const groups = mysqlTable('groups', {
  id: varchar('id', { length: 255 }).primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  description: text('description'),
  visibility: mysqlEnum('visibility', ['public', 'private-visible', 'private-hidden']).default('public').notNull(),
  coverImage: varchar('coverImage', { length: 255 }),
  category: varchar('category', { length: 100 }),
  rules: text('rules'),
  creatorId: varchar('creatorId', { length: 255 }).notNull(),
  postPermission: mysqlEnum('postPermission', ['all-members', 'admin-only']).default('all-members').notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// Group relations
export const groupsRelations = relations(groups, ({ one, many }) => ({
  creator: one(users, {
    fields: [groups.creatorId],
    references: [users.id],
  }),
  members: many(groupMembers),
  posts: many(posts),
  reports: many(groupReports),
}));

// Group members table
export const groupMembers = mysqlTable('group_members', {
  id: varchar('id', { length: 255 }).primaryKey(),
  groupId: varchar('groupId', { length: 255 }).notNull(),
  userId: varchar('userId', { length: 255 }).notNull(),
  role: mysqlEnum('role', ['admin', 'moderator', 'member', 'pending']).default('member').notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// Group members relations
export const groupMembersRelations = relations(groupMembers, ({ one }) => ({
  group: one(groups, {
    fields: [groupMembers.groupId],
    references: [groups.id],
  }),
  user: one(users, {
    fields: [groupMembers.userId],
    references: [users.id],
  }),
}));

// Group reports table
export const groupReports = mysqlTable('group_reports', {
  id: varchar('id', { length: 255 }).primaryKey(),
  groupId: varchar('groupId', { length: 255 }).notNull(),
  reporterId: varchar('reporterId', { length: 255 }).notNull(),
  reportedUserId: varchar('reportedUserId', { length: 255 }),
  postId: varchar('postId', { length: 255 }),
  reason: mysqlEnum('reason', ['spam', 'harassment', 'inappropriate_content', 'violation', 'other']).notNull(),
  description: text('description'),
  status: mysqlEnum('status', ['pending', 'reviewed']).default('pending').notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
});

// Group reports relations
export const groupReportsRelations = relations(groupReports, ({ one }) => ({
  group: one(groups, {
    fields: [groupReports.groupId],
    references: [groups.id],
  }),
  reporter: one(users, {
    fields: [groupReports.reporterId],
    references: [users.id],
  }),
  reportedUser: one(users, {
    fields: [groupReports.reportedUserId],
    references: [users.id],
  }),
  post: one(posts, {
    fields: [groupReports.postId],
    references: [posts.id],
  }),
}));

// Events table
export const events = mysqlTable('events', {
  id: varchar('id', { length: 255 }).primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  description: text('description'),
  startTime: timestamp('startTime').notNull(),
  endTime: timestamp('endTime').notNull(),
  location: varchar('location', { length: 255 }),
  isOnline: boolean('isOnline').default(false),
  onlineLink: varchar('onlineLink', { length: 255 }),
  coverImage: varchar('coverImage', { length: 255 }),
  hostId: varchar('hostId', { length: 255 }).notNull(),
  visibility: mysqlEnum('visibility', ['public', 'private', 'friends']).default('public').notNull(),
  category: varchar('category', { length: 100 }),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// Event relations
export const eventsRelations = relations(events, ({ one, many }) => ({
  host: one(users, {
    fields: [events.hostId],
    references: [users.id],
    relationName: 'host',
  }),
  attendees: many(eventAttendees),
  invites: many(eventInvites),
  comments: many(eventComments),
}));

// Event attendees table
export const eventAttendees = mysqlTable('event_attendees', {
  id: varchar('id', { length: 255 }).primaryKey(),
  eventId: varchar('eventId', { length: 255 }).notNull(),
  userId: varchar('userId', { length: 255 }).notNull(),
  status: mysqlEnum('status', ['going', 'interested', 'not_going']).notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// Event attendees relations
export const eventAttendeesRelations = relations(eventAttendees, ({ one }) => ({
  event: one(events, {
    fields: [eventAttendees.eventId],
    references: [events.id],
  }),
  user: one(users, {
    fields: [eventAttendees.userId],
    references: [users.id],
  }),
}));

// Event invites table
export const eventInvites = mysqlTable('event_invites', {
  id: varchar('id', { length: 255 }).primaryKey(),
  eventId: varchar('eventId', { length: 255 }).notNull(),
  fromUserId: varchar('fromUserId', { length: 255 }).notNull(),
  toUserId: varchar('toUserId', { length: 255 }).notNull(),
  status: mysqlEnum('status', ['pending', 'accepted', 'declined']).default('pending').notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// Event invites relations
export const eventInvitesRelations = relations(eventInvites, ({ one }) => ({
  event: one(events, {
    fields: [eventInvites.eventId],
    references: [events.id],
  }),
  sender: one(users, {
    fields: [eventInvites.fromUserId],
    references: [users.id],
    relationName: 'sender',
  }),
  recipient: one(users, {
    fields: [eventInvites.toUserId],
    references: [users.id],
    relationName: 'recipient',
  }),
}));

// Event comments table
export const eventComments = mysqlTable('event_comments', {
  id: varchar('id', { length: 255 }).primaryKey(),
  eventId: varchar('eventId', { length: 255 }).notNull(),
  userId: varchar('userId', { length: 255 }).notNull(),
  content: text('content').notNull(),
  images: json('images').$type<string[]>(),
  videos: json('videos').$type<string[]>(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// Event comments relations
export const eventCommentsRelations = relations(eventComments, ({ one }) => ({
  event: one(events, {
    fields: [eventComments.eventId],
    references: [events.id],
  }),
  user: one(users, {
    fields: [eventComments.userId],
    references: [users.id],
  }),
}));

// Marketplace - Stores table
export const stores = mysqlTable('stores', {
  id: varchar('id', { length: 255 }).primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  slug: varchar('slug', { length: 100 }).notNull().unique(),
  description: text('description'),
  logo: varchar('logo', { length: 255 }),
  banner: varchar('banner', { length: 255 }),
  location: varchar('location', { length: 255 }),
  phone: varchar('phone', { length: 50 }),
  email: varchar('email', { length: 255 }),
  website: varchar('website', { length: 255 }),
  ownerId: varchar('ownerId', { length: 255 }).notNull(),
  isVerified: boolean('isVerified').default(false),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// Store relations
export const storesRelations = relations(stores, ({ one, many }) => ({
  owner: one(users, {
    fields: [stores.ownerId],
    references: [users.id],
  }),
  products: many(products),
  followers: many(storeFollows),
  reviews: many(storeReviews),
  settings: one(storeSettings),
}));

// Store follows table
export const storeFollows = mysqlTable('store_follows', {
  id: varchar('id', { length: 255 }).primaryKey(),
  userId: varchar('userId', { length: 255 }).notNull(),
  storeId: varchar('storeId', { length: 255 }).notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
});

// Store follows relations
export const storeFollowsRelations = relations(storeFollows, ({ one }) => ({
  user: one(users, {
    fields: [storeFollows.userId],
    references: [users.id],
  }),
  store: one(stores, {
    fields: [storeFollows.storeId],
    references: [stores.id],
  }),
}));

// Store reviews table
export const storeReviews = mysqlTable('store_reviews', {
  id: varchar('id', { length: 255 }).primaryKey(),
  userId: varchar('userId', { length: 255 }).notNull(),
  storeId: varchar('storeId', { length: 255 }).notNull(),
  rating: int('rating').notNull(),
  comment: text('comment'),
  isApproved: boolean('isApproved').default(true),
  isReported: boolean('isReported').default(false),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// Store reviews relations
export const storeReviewsRelations = relations(storeReviews, ({ one }) => ({
  user: one(users, {
    fields: [storeReviews.userId],
    references: [users.id],
  }),
  store: one(stores, {
    fields: [storeReviews.storeId],
    references: [stores.id],
  }),
}));

// Products table
export const products = mysqlTable('products', {
  id: varchar('id', { length: 255 }).primaryKey(),
  title: varchar('title', { length: 255 }).notNull(),
  description: text('description'),
  price: int('price').notNull(),
  item_condition: mysqlEnum('item_condition', ['new', 'like_new', 'good', 'fair', 'poor']).notNull(),
  category: varchar('category', { length: 100 }).notNull(),
  location: varchar('location', { length: 255 }),
  photos: json('photos').$type<string[]>(),
  storeId: varchar('storeId', { length: 255 }).notNull(),
  viewCount: int('viewCount').default(0),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// Products relations
export const productsRelations = relations(products, ({ one, many }) => ({
  store: one(stores, {
    fields: [products.storeId],
    references: [stores.id],
  }),
  reports: many(productReports),
}));

// Product reports table
export const productReports = mysqlTable('product_reports', {
  id: varchar('id', { length: 255 }).primaryKey(),
  productId: varchar('productId', { length: 255 }).notNull(),
  userId: varchar('userId', { length: 255 }).notNull(),
  reason: mysqlEnum('reason', ['counterfeit', 'inappropriate', 'fraud', 'prohibited', 'other']).notNull(),
  description: text('description'),
  status: mysqlEnum('status', ['pending', 'reviewed', 'resolved', 'dismissed']).default('pending').notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// Product reports relations
export const productReportsRelations = relations(productReports, ({ one }) => ({
  product: one(products, {
    fields: [productReports.productId],
    references: [products.id],
  }),
  user: one(users, {
    fields: [productReports.userId],
    references: [users.id],
  }),
}));

// Store settings table
export const storeSettings = mysqlTable('store_settings', {
  id: varchar('id', { length: 255 }).primaryKey(),
  storeId: varchar('storeId', { length: 255 }).notNull().unique(),
  visibility: mysqlEnum('visibility', ['public', 'private']).default('public').notNull(),
  showOutOfStock: boolean('showOutOfStock').default(true),
  showProductViews: boolean('showProductViews').default(true),
  emailNotifications: boolean('emailNotifications').default(true),
  productViewNotifications: boolean('productViewNotifications').default(false),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// Store settings relations
export const storeSettingsRelations = relations(storeSettings, ({ one }) => ({
  store: one(stores, {
    fields: [storeSettings.storeId],
    references: [stores.id],
  }),
}));

// ============================================================================
// FAN PAGES SYSTEM TABLES
// ============================================================================

// Fan Pages table
export const fanPages = mysqlTable('fan_pages', {
  id: varchar('id', { length: 255 }).primaryKey(),
  ownerId: varchar('ownerId', { length: 255 }).notNull(),
  name: varchar('name', { length: 255 }).notNull(),
  username: varchar('username', { length: 50 }).notNull().unique(),
  category: mysqlEnum('category', [
    'musician', 'actor', 'brand', 'business', 'organization',
    'public_figure', 'artist', 'writer', 'athlete', 'politician',
    'entertainment', 'media', 'community', 'cause', 'other'
  ]).notNull(),
  description: text('description'),
  profileImage: varchar('profileImage', { length: 255 }),
  coverImage: varchar('coverImage', { length: 255 }),
  website: varchar('website', { length: 255 }),
  email: varchar('email', { length: 255 }),
  phone: varchar('phone', { length: 50 }),
  location: varchar('location', { length: 255 }),
  isVerified: boolean('isVerified').default(false),
  isActive: boolean('isActive').default(true),
  followerCount: int('followerCount').default(0),
  postCount: int('postCount').default(0),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// Fan Page relations
export const fanPagesRelations = relations(fanPages, ({ one, many }) => ({
  owner: one(users, {
    fields: [fanPages.ownerId],
    references: [users.id],
  }),
  followers: many(fanPageFollowers),
  posts: many(fanPagePosts),
  roles: many(fanPageRoles),
  settings: one(fanPageSettings),
}));

// Fan Page Followers table
export const fanPageFollowers = mysqlTable('fan_page_followers', {
  id: varchar('id', { length: 255 }).primaryKey(),
  userId: varchar('userId', { length: 255 }).notNull(),
  fanPageId: varchar('fanPageId', { length: 255 }).notNull(),
  createdAt: timestamp('followedAt').defaultNow().notNull(), // Maps to followedAt in DB
});

// Fan Page Followers relations
export const fanPageFollowersRelations = relations(fanPageFollowers, ({ one }) => ({
  user: one(users, {
    fields: [fanPageFollowers.userId],
    references: [users.id],
  }),
  fanPage: one(fanPages, {
    fields: [fanPageFollowers.fanPageId],
    references: [fanPages.id],
  }),
}));

// Fan Page Posts table
export const fanPagePosts = mysqlTable('fan_page_posts', {
  id: varchar('id', { length: 255 }).primaryKey(),
  fanPageId: varchar('fanPageId', { length: 255 }).notNull(),
  content: text('content'),
  images: json('images').$type<string[]>(),
  videos: json('videos').$type<string[]>(),
  type: mysqlEnum('type', ['text', 'image', 'video', 'link', 'event']).default('text').notNull(),
  scheduledAt: timestamp('scheduledAt'),
  isPublished: boolean('isPublished').default(true),
  likeCount: int('likeCount').default(0),
  commentCount: int('commentCount').default(0),
  shareCount: int('shareCount').default(0),
  viewCount: int('viewCount').default(0),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// Fan Page Posts relations
export const fanPagePostsRelations = relations(fanPagePosts, ({ one, many }) => ({
  fanPage: one(fanPages, {
    fields: [fanPagePosts.fanPageId],
    references: [fanPages.id],
  }),
  likes: many(fanPagePostLikes),
  comments: many(fanPagePostComments),
}));

// Fan Page Post Likes table
export const fanPagePostLikes = mysqlTable('fan_page_post_likes', {
  id: varchar('id', { length: 255 }).primaryKey(),
  userId: varchar('userId', { length: 255 }).notNull(),
  fanPagePostId: varchar('fanPagePostId', { length: 255 }).notNull(),
  type: mysqlEnum('type', ['like', 'love', 'wow', 'haha', 'sad', 'angry']).default('like').notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
});

// Fan Page Post Likes relations
export const fanPagePostLikesRelations = relations(fanPagePostLikes, ({ one }) => ({
  user: one(users, {
    fields: [fanPagePostLikes.userId],
    references: [users.id],
  }),
  fanPagePost: one(fanPagePosts, {
    fields: [fanPagePostLikes.fanPagePostId],
    references: [fanPagePosts.id],
  }),
}));

// Fan Page Post Comments table
export const fanPagePostComments = mysqlTable('fan_page_post_comments', {
  id: varchar('id', { length: 255 }).primaryKey(),
  postId: varchar('fanPagePostId', { length: 255 }).notNull(), // Maps to fanPagePostId in DB
  userId: varchar('userId', { length: 255 }),
  fanPageId: varchar('fanPageId', { length: 255 }), // If comment is from a page
  content: text('content').notNull(),
  parentId: varchar('parentId', { length: 255 }), // For nested comments
  likeCount: int('likeCount').default(0),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// Fan Page Post Comment Likes table
export const fanPagePostCommentLikes = mysqlTable('fan_page_post_comment_likes', {
  id: varchar('id', { length: 255 }).primaryKey(),
  commentId: varchar('commentId', { length: 255 }).notNull(),
  userId: varchar('userId', { length: 255 }).notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
});

// Fan Page Post Comments relations
export const fanPagePostCommentsRelations = relations(fanPagePostComments, ({ one, many }) => ({
  fanPagePost: one(fanPagePosts, {
    fields: [fanPagePostComments.postId],
    references: [fanPagePosts.id],
  }),
  user: one(users, {
    fields: [fanPagePostComments.userId],
    references: [users.id],
  }),
  fanPage: one(fanPages, {
    fields: [fanPagePostComments.fanPageId],
    references: [fanPages.id],
  }),
  parent: one(fanPagePostComments, {
    fields: [fanPagePostComments.parentId],
    references: [fanPagePostComments.id],
    relationName: 'parentComment',
  }),
  replies: many(fanPagePostComments, { relationName: 'parentComment' }),
  likes: many(fanPagePostCommentLikes),
}));

// Fan Page Post Comment Likes relations
export const fanPagePostCommentLikesRelations = relations(fanPagePostCommentLikes, ({ one }) => ({
  comment: one(fanPagePostComments, {
    fields: [fanPagePostCommentLikes.commentId],
    references: [fanPagePostComments.id],
  }),
  user: one(users, {
    fields: [fanPagePostCommentLikes.userId],
    references: [users.id],
  }),
}));

// Fan Page Roles table
export const fanPageRoles = mysqlTable('fan_page_roles', {
  id: varchar('id', { length: 255 }).primaryKey(),
  fanPageId: varchar('fanPageId', { length: 255 }).notNull(),
  userId: varchar('userId', { length: 255 }).notNull(),
  role: mysqlEnum('role', ['admin', 'editor', 'moderator']).notNull(),
  addedBy: varchar('addedBy', { length: 255 }).notNull(),
  permissions: json('permissions').$type<string[]>(), // Custom permissions array
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// Fan Page Roles relations
export const fanPageRolesRelations = relations(fanPageRoles, ({ one }) => ({
  fanPage: one(fanPages, {
    fields: [fanPageRoles.fanPageId],
    references: [fanPages.id],
  }),
  user: one(users, {
    fields: [fanPageRoles.userId],
    references: [users.id],
  }),
  addedByUser: one(users, {
    fields: [fanPageRoles.addedBy],
    references: [users.id],
  }),
}));

// Fan Page Settings table
export const fanPageSettings = mysqlTable('fan_page_settings', {
  id: varchar('id', { length: 255 }).primaryKey(),
  fanPageId: varchar('fanPageId', { length: 255 }).notNull().unique(),
  messagingEnabled: boolean('messagingEnabled').default(true),
  postVisibility: mysqlEnum('postVisibility', ['public', 'followers']).default('public'),
  allowComments: boolean('allowComments').default(true),
  allowSharing: boolean('allowSharing').default(true),
  moderateComments: boolean('moderateComments').default(false),
  autoReply: text('autoReply'),
  emailNotifications: boolean('emailNotifications').default(true),
  followerNotifications: boolean('followerNotifications').default(true),
  postNotifications: boolean('postNotifications').default(true),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// Fan Page Settings relations
export const fanPageSettingsRelations = relations(fanPageSettings, ({ one }) => ({
  fanPage: one(fanPages, {
    fields: [fanPageSettings.fanPageId],
    references: [fanPages.id],
  }),
}));

// ============================================================================
// WALLET SYSTEM TABLES
// ============================================================================

// Wallets table - User wallet balances
export const wallets = mysqlTable('wallets', {
  id: varchar('id', { length: 255 }).primaryKey(),
  userId: varchar('userId', { length: 255 }).notNull().unique(),
  generalBalance: decimal('generalBalance', { precision: 15, scale: 2 }).default('0.00').notNull(),
  earningBalance: decimal('earningBalance', { precision: 15, scale: 2 }).default('0.00').notNull(),
  totalDeposited: decimal('totalDeposited', { precision: 15, scale: 2 }).default('0.00').notNull(),
  totalWithdrawn: decimal('totalWithdrawn', { precision: 15, scale: 2 }).default('0.00').notNull(),
  totalSent: decimal('totalSent', { precision: 15, scale: 2 }).default('0.00').notNull(),
  totalReceived: decimal('totalReceived', { precision: 15, scale: 2 }).default('0.00').notNull(),
  isActive: boolean('isActive').default(true).notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// Wallet relations
export const walletsRelations = relations(wallets, ({ one, many }) => ({
  user: one(users, {
    fields: [wallets.userId],
    references: [users.id],
  }),
  transactions: many(walletTransactions),
  pinCodes: many(pinCodes),
}));

// Wallet transactions table
export const walletTransactions = mysqlTable('wallet_transactions', {
  id: varchar('id', { length: 255 }).primaryKey(),
  userId: varchar('userId', { length: 255 }).notNull(),
  type: mysqlEnum('type', ['deposit', 'send', 'receive', 'cashout', 'internal_transfer', 'earning', 'withdraw']).notNull(),
  amount: decimal('amount', { precision: 15, scale: 2 }).notNull(),
  fee: decimal('fee', { precision: 15, scale: 2 }).default('0.00').notNull(),
  netAmount: decimal('netAmount', { precision: 15, scale: 2 }).notNull(),
  walletType: mysqlEnum('walletType', ['general', 'earning']).notNull(),
  toUserId: varchar('toUserId', { length: 255 }),
  toAgentId: varchar('toAgentId', { length: 255 }),
  fromWalletType: mysqlEnum('fromWalletType', ['general', 'earning']),
  toWalletType: mysqlEnum('toWalletType', ['general', 'earning']),
  status: mysqlEnum('status', ['pending', 'processing', 'completed', 'failed', 'cancelled']).default('pending').notNull(),
  paymentGateway: varchar('paymentGateway', { length: 100 }),
  gatewayTransactionId: varchar('gatewayTransactionId', { length: 255 }),
  reference: varchar('reference', { length: 255 }),
  note: text('note'),
  metadata: json('metadata'),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// Wallet transactions relations
export const walletTransactionsRelations = relations(walletTransactions, ({ one }) => ({
  user: one(users, {
    fields: [walletTransactions.userId],
    references: [users.id],
  }),
  toUser: one(users, {
    fields: [walletTransactions.toUserId],
    references: [users.id],
  }),
  toAgent: one(agents, {
    fields: [walletTransactions.toAgentId],
    references: [agents.id],
  }),
  wallet: one(wallets, {
    fields: [walletTransactions.userId],
    references: [wallets.userId],
  }),
}));

// Agents table - For cashout services
export const agents = mysqlTable('agents', {
  id: varchar('id', { length: 255 }).primaryKey(),
  userId: varchar('userId', { length: 255 }).notNull(),
  name: varchar('name', { length: 255 }).notNull(),
  phone: varchar('phone', { length: 50 }).notNull(),
  email: varchar('email', { length: 255 }),
  location: varchar('location', { length: 255 }),
  serviceType: varchar('serviceType', { length: 100 }).notNull(), // bKash, Nagad, Bank, etc.
  accountNumber: varchar('accountNumber', { length: 100 }).notNull(),
  accountName: varchar('accountName', { length: 255 }).notNull(),
  dailyLimit: decimal('dailyLimit', { precision: 15, scale: 2 }).default('10000.00').notNull(),
  currentDailyAmount: decimal('currentDailyAmount', { precision: 15, scale: 2 }).default('0.00').notNull(),
  commission: decimal('commission', { precision: 5, scale: 2 }).default('2.00').notNull(), // Percentage
  isActive: boolean('isActive').default(true).notNull(),
  isVerified: boolean('isVerified').default(false).notNull(),
  rating: decimal('rating', { precision: 3, scale: 2 }).default('0.00'),
  totalTransactions: int('totalTransactions').default(0),
  totalAmount: decimal('totalAmount', { precision: 15, scale: 2 }).default('0.00'),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// Agents relations
export const agentsRelations = relations(agents, ({ one, many }) => ({
  user: one(users, {
    fields: [agents.userId],
    references: [users.id],
  }),
  cashoutRequests: many(cashoutRequests),
  transactions: many(walletTransactions),
}));

// Payment gateways table
export const paymentGateways = mysqlTable('payment_gateways', {
  id: varchar('id', { length: 255 }).primaryKey(),
  name: varchar('name', { length: 100 }).notNull(),
  displayName: varchar('displayName', { length: 100 }).notNull(),
  type: mysqlEnum('type', ['stripe', 'paypal', 'sslcommerz', 'bkash', 'nagad', 'rocket', 'bank', 'uddoktapay', 'manual', 'wallet']).notNull(),
  isActive: boolean('isActive').default(false).notNull(),
  config: json('config').notNull(), // Store API keys, secrets, etc.
  depositFee: decimal('depositFee', { precision: 5, scale: 2 }).default('0.00'), // Percentage
  depositFixedFee: decimal('depositFixedFee', { precision: 10, scale: 2 }).default('0.00'), // Fixed amount
  minDeposit: decimal('minDeposit', { precision: 10, scale: 2 }).default('1.00'),
  maxDeposit: decimal('maxDeposit', { precision: 15, scale: 2 }).default('10000.00'),
  currency: varchar('currency', { length: 10 }).default('USD').notNull(),
  sortOrder: int('sortOrder').default(0),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// Wallet settings table
export const walletSettings = mysqlTable('wallet_settings', {
  id: varchar('id', { length: 255 }).primaryKey(),
  key: varchar('key', { length: 100 }).notNull().unique(),
  value: text('value').notNull(),
  type: mysqlEnum('type', ['string', 'number', 'boolean', 'json']).default('string').notNull(),
  description: text('description'),
  category: varchar('category', { length: 100 }).default('general'),
  isSystem: boolean('isSystem').default(false).notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// PIN codes table
export const pinCodes = mysqlTable('pin_codes', {
  id: varchar('id', { length: 255 }).primaryKey(),
  userId: varchar('userId', { length: 255 }).notNull(),
  pinHash: varchar('pinHash', { length: 255 }).notNull(),
  isActive: boolean('isActive').default(true).notNull(),
  failedAttempts: int('failedAttempts').default(0).notNull(),
  lockedUntil: timestamp('lockedUntil'),
  lastUsedAt: timestamp('lastUsedAt'),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// PIN codes relations
export const pinCodesRelations = relations(pinCodes, ({ one }) => ({
  user: one(users, {
    fields: [pinCodes.userId],
    references: [users.id],
  }),
  wallet: one(wallets, {
    fields: [pinCodes.userId],
    references: [wallets.userId],
  }),
}));

// User payment methods table
export const userPaymentMethods = mysqlTable('user_payment_methods', {
  id: varchar('id', { length: 255 }).primaryKey(),
  userId: varchar('userId', { length: 255 }).notNull(),
  type: mysqlEnum('type', ['bank', 'mobile_banking', 'card']).notNull(),
  methodType: mysqlEnum('methodType', ['payment', 'payout']).notNull(), // payment for deposits, payout for withdrawals
  name: varchar('name', { length: 255 }).notNull(),
  details: json('details').notNull(), // Store account details
  isDefault: boolean('isDefault').default(false).notNull(),
  isActive: boolean('isActive').default(true).notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// User payment methods relations
export const userPaymentMethodsRelations = relations(userPaymentMethods, ({ one }) => ({
  user: one(users, {
    fields: [userPaymentMethods.userId],
    references: [users.id],
  }),
}));

// Cashout requests table
export const cashoutRequests = mysqlTable('cashout_requests', {
  id: varchar('id', { length: 255 }).primaryKey(),
  userId: varchar('userId', { length: 255 }).notNull(),
  agentId: varchar('agentId', { length: 255 }).notNull(),
  amount: decimal('amount', { precision: 15, scale: 2 }).notNull(),
  fee: decimal('fee', { precision: 15, scale: 2 }).default('0.00').notNull(),
  netAmount: decimal('netAmount', { precision: 15, scale: 2 }).notNull(),
  status: mysqlEnum('status', ['pending', 'accepted', 'processing', 'completed', 'declined', 'cancelled']).default('pending').notNull(),
  note: text('note'),
  agentNote: text('agentNote'),
  transactionId: varchar('transactionId', { length: 255 }), // Links to wallet_transactions
  processedAt: timestamp('processedAt'),
  completedAt: timestamp('completedAt'),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// Referral Codes table
export const referralCodes = mysqlTable('referral_codes', {
  id: varchar('id', { length: 255 }).primaryKey(),
  userId: varchar('userId', { length: 255 }).notNull().unique(),
  code: varchar('code', { length: 50 }).notNull().unique(),
  isActive: boolean('isActive').default(true).notNull(),
  totalReferrals: int('totalReferrals').default(0).notNull(),
  totalEarnings: decimal('totalEarnings', { precision: 15, scale: 2 }).default('0.00').notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// Referrals table
export const referrals = mysqlTable('referrals', {
  id: varchar('id', { length: 255 }).primaryKey(),
  referrerId: varchar('referrerId', { length: 255 }).notNull(),
  referredUserId: varchar('referredUserId', { length: 255 }).notNull(),
  referralCode: varchar('referralCode', { length: 50 }).notNull(),
  status: mysqlEnum('status', ['pending', 'completed', 'cancelled']).default('pending').notNull(),
  rewardAmount: decimal('rewardAmount', { precision: 15, scale: 2 }).default('5.00').notNull(),
  paidAt: timestamp('paidAt'),
  completedAt: timestamp('completedAt'),
  metadata: json('metadata'),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// Referral Settings table
export const referralSettings = mysqlTable('referral_settings', {
  id: varchar('id', { length: 255 }).primaryKey(),
  isEnabled: boolean('isEnabled').default(true).notNull(),
  rewardAmount: decimal('rewardAmount', { precision: 15, scale: 2 }).default('5.00').notNull(),
  minPayoutThreshold: decimal('minPayoutThreshold', { precision: 15, scale: 2 }).default('10.00').notNull(),
  requiresVerification: boolean('requiresVerification').default(false).notNull(),
  maxReferralsPerUser: int('maxReferralsPerUser').default(100).notNull(),
  rewardBothUsers: boolean('rewardBothUsers').default(false).notNull(),
  referredUserReward: decimal('referredUserReward', { precision: 15, scale: 2 }).default('0.00').notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// Referral Commission Settings table
export const referralCommissionSettings = mysqlTable('referral_commission_settings', {
  id: varchar('id', { length: 255 }).primaryKey(),
  isEnabled: boolean('isEnabled').default(true).notNull(),

  // Commission Types
  firstPurchaseEnabled: boolean('firstPurchaseEnabled').default(true).notNull(),
  recurringPurchaseEnabled: boolean('recurringPurchaseEnabled').default(false).notNull(),

  // Default Commission Rates
  defaultCommissionType: mysqlEnum('defaultCommissionType', ['fixed', 'percentage']).default('percentage').notNull(),
  defaultCommissionValue: decimal('defaultCommissionValue', { precision: 15, scale: 2 }).default('10.00').notNull(),
  defaultRecurringCommissionValue: decimal('defaultRecurringCommissionValue', { precision: 15, scale: 2 }).default('5.00').notNull(),

  // Limits and Eligibility
  maxCommissionAmount: decimal('maxCommissionAmount', { precision: 15, scale: 2 }).default('100.00').notNull(),
  eligibilityPeriodDays: int('eligibilityPeriodDays').default(30).notNull(), // Days after referral signup
  minSubscriptionAmount: decimal('minSubscriptionAmount', { precision: 15, scale: 2 }).default('1.00').notNull(),

  // Additional Settings
  requireActiveReferrer: boolean('requireActiveReferrer').default(true).notNull(),
  maxCommissionsPerReferral: int('maxCommissionsPerReferral').default(0).notNull(), // 0 = unlimited

  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// Package-specific Commission Settings
export const packageCommissionSettings = mysqlTable('package_commission_settings', {
  id: varchar('id', { length: 255 }).primaryKey(),
  planId: varchar('planId', { length: 255 }).notNull(),
  planName: varchar('planName', { length: 255 }).notNull(),

  // Override default settings
  isEnabled: boolean('isEnabled').default(true).notNull(),
  commissionType: mysqlEnum('commissionType', ['fixed', 'percentage']).notNull(),
  firstPurchaseCommission: decimal('firstPurchaseCommission', { precision: 15, scale: 2 }).notNull(),
  recurringCommission: decimal('recurringCommission', { precision: 15, scale: 2 }).default('0.00').notNull(),
  maxCommissionAmount: decimal('maxCommissionAmount', { precision: 15, scale: 2 }),

  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// Referral Commissions table
export const referralCommissions = mysqlTable('referral_commissions', {
  id: varchar('id', { length: 255 }).primaryKey(),
  referralId: varchar('referralId', { length: 255 }).notNull(), // Links to referrals table
  referrerId: varchar('referrerId', { length: 255 }).notNull(),
  referredUserId: varchar('referredUserId', { length: 255 }).notNull(),

  // Subscription Details
  subscriptionId: varchar('subscriptionId', { length: 255 }).notNull(),
  subscriptionTransactionId: varchar('subscriptionTransactionId', { length: 255 }).notNull(),
  planId: varchar('planId', { length: 255 }).notNull(),
  planName: varchar('planName', { length: 255 }).notNull(),
  subscriptionAmount: decimal('subscriptionAmount', { precision: 15, scale: 2 }).notNull(),

  // Commission Details
  commissionType: mysqlEnum('commissionType', ['fixed', 'percentage']).notNull(),
  commissionRate: decimal('commissionRate', { precision: 15, scale: 2 }).notNull(),
  commissionAmount: decimal('commissionAmount', { precision: 15, scale: 2 }).notNull(),
  isFirstPurchase: boolean('isFirstPurchase').default(true).notNull(),

  // Status and Processing
  status: mysqlEnum('status', ['pending', 'approved', 'paid', 'cancelled']).default('pending').notNull(),
  approvedAt: timestamp('approvedAt'),
  paidAt: timestamp('paidAt'),
  cancelledAt: timestamp('cancelledAt'),
  cancelReason: text('cancelReason'),

  // Wallet Integration
  walletTransactionId: varchar('walletTransactionId', { length: 255 }),

  // Metadata
  metadata: json('metadata'),
  notes: text('notes'),

  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow().onUpdateNow().notNull(),
});

// Cashout requests relations
export const cashoutRequestsRelations = relations(cashoutRequests, ({ one }) => ({
  user: one(users, {
    fields: [cashoutRequests.userId],
    references: [users.id],
  }),
  agent: one(agents, {
    fields: [cashoutRequests.agentId],
    references: [agents.id],
  }),
  transaction: one(walletTransactions, {
    fields: [cashoutRequests.transactionId],
    references: [walletTransactions.id],
  }),
}));

// Referral Code relations
export const referralCodesRelations = relations(referralCodes, ({ one, many }) => ({
  user: one(users, {
    fields: [referralCodes.userId],
    references: [users.id],
  }),
  referrals: many(referrals),
}));

// Referral relations
export const referralsRelations = relations(referrals, ({ one, many }) => ({
  referrer: one(users, {
    fields: [referrals.referrerId],
    references: [users.id],
    relationName: 'referrer',
  }),
  referredUser: one(users, {
    fields: [referrals.referredUserId],
    references: [users.id],
    relationName: 'referred',
  }),
  referralCode: one(referralCodes, {
    fields: [referrals.referralCode],
    references: [referralCodes.code],
  }),
  commissions: many(referralCommissions),
}));

// Referral Commission relations
export const referralCommissionsRelations = relations(referralCommissions, ({ one }) => ({
  referral: one(referrals, {
    fields: [referralCommissions.referralId],
    references: [referrals.id],
  }),
  referrer: one(users, {
    fields: [referralCommissions.referrerId],
    references: [users.id],
    relationName: 'commissionReferrer',
  }),
  referredUser: one(users, {
    fields: [referralCommissions.referredUserId],
    references: [users.id],
    relationName: 'commissionReferred',
  }),
  subscription: one(userSubscriptions, {
    fields: [referralCommissions.subscriptionId],
    references: [userSubscriptions.id],
  }),
  subscriptionTransaction: one(subscriptionTransactions, {
    fields: [referralCommissions.subscriptionTransactionId],
    references: [subscriptionTransactions.id],
  }),
}));

// Package Commission Settings relations
export const packageCommissionSettingsRelations = relations(packageCommissionSettings, ({ one }) => ({
  plan: one(subscriptionPlans, {
    fields: [packageCommissionSettings.planId],
    references: [subscriptionPlans.id],
  }),
}));
