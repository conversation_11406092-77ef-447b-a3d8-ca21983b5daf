"use client";

import { useState, useEffect } from "react";
import { AdminLayout } from "@/components/admin/AdminLayout";
import { Button } from "@/components/ui/Button";
import { Card } from "@/components/ui/Card";
import { Spinner } from "@/components/ui/Spinner";
import { Badge } from "@/components/ui/Badge";
import { PlanBadge } from "@/components/ui/PlanBadge";
import { toast } from "react-hot-toast";
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  CheckCircleIcon,
  XCircleIcon,
  ArrowUpIcon,
  ArrowDownIcon,
} from "@heroicons/react/24/outline";
import Link from "next/link";

interface SubscriptionPlan {
  id: string;
  name: string;
  displayName: string;
  description: string | null;
  price: string;
  currency: string;
  billingCycle: 'monthly' | 'yearly';
  features: string[];
  maxPosts: number;
  maxStorage: number;
  maxGroups: number;
  canCreateFanPages: boolean;
  canCreateStores: boolean;
  canMonetizeBlogs: boolean;
  prioritySupport: boolean;
  // Badge system fields
  badgeType: 'none' | 'crown' | 'star' | 'diamond' | 'vip' | 'custom';
  badgeColor: string;
  customBadgeUrl: string | null;
  badgePriority: number;
  isActive: boolean;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
}

export default function SubscriptionPlansPage() {
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);

  useEffect(() => {
    fetchPlans();
  }, []);

  const fetchPlans = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/admin/subscriptions/plans');
      if (!response.ok) {
        throw new Error('Failed to fetch subscription plans');
      }
      const data = await response.json();
      setPlans(data.plans || []);
    } catch (error) {
      console.error('Error fetching plans:', error);
      toast.error('Failed to load subscription plans');
    } finally {
      setIsLoading(false);
    }
  };

  const togglePlanStatus = async (planId: string, currentStatus: boolean) => {
    try {
      const response = await fetch(`/api/admin/subscriptions/plans/${planId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          isActive: !currentStatus,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update plan status');
      }

      await fetchPlans();
      toast.success(`Plan ${!currentStatus ? 'activated' : 'deactivated'} successfully`);
    } catch (error) {
      console.error('Error updating plan status:', error);
      toast.error('Failed to update plan status');
    }
  };

  const deletePlan = async (planId: string) => {
    if (!confirm('Are you sure you want to delete this subscription plan? This action cannot be undone.')) {
      return;
    }

    try {
      setIsDeleting(planId);
      const response = await fetch(`/api/admin/subscriptions/plans/${planId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete plan');
      }

      await fetchPlans();
      toast.success('Plan deleted successfully');
    } catch (error) {
      console.error('Error deleting plan:', error);
      toast.error('Failed to delete plan');
    } finally {
      setIsDeleting(null);
    }
  };

  const updateSortOrder = async (planId: string, direction: 'up' | 'down') => {
    try {
      const response = await fetch(`/api/admin/subscriptions/plans/${planId}/sort`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ direction }),
      });

      if (!response.ok) {
        throw new Error('Failed to update sort order');
      }

      await fetchPlans();
      toast.success('Sort order updated successfully');
    } catch (error) {
      console.error('Error updating sort order:', error);
      toast.error('Failed to update sort order');
    }
  };

  const formatPrice = (price: string, currency: string, billingCycle: string) => {
    const amount = parseFloat(price);
    return `${currency} ${amount.toFixed(2)}/${billingCycle === 'yearly' ? 'year' : 'month'}`;
  };

  const formatFeatures = (features: string[]) => {
    if (!features || features.length === 0) return 'No features listed';
    return features.slice(0, 3).join(', ') + (features.length > 3 ? ` +${features.length - 3} more` : '');
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <Spinner size="lg" />
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Subscription Plans</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage subscription plans, pricing, and features
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <Link href="/admin/subscriptions/plans/new">
            <Button>
              <PlusIcon className="mr-2 h-5 w-5" />
              Create Plan
            </Button>
          </Link>
        </div>
      </div>

      {plans.length === 0 ? (
        <Card className="p-8 text-center">
          <div className="mx-auto h-12 w-12 text-gray-400">
            <PlusIcon className="h-12 w-12" />
          </div>
          <h3 className="mt-4 text-lg font-medium text-gray-900">No subscription plans</h3>
          <p className="mt-2 text-sm text-gray-500">
            Get started by creating your first subscription plan.
          </p>
          <div className="mt-6">
            <Link href="/admin/subscriptions/plans/new">
              <Button>
                <PlusIcon className="mr-2 h-5 w-5" />
                Create Plan
              </Button>
            </Link>
          </div>
        </Card>
      ) : (
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2 xl:grid-cols-3">
          {plans
            .sort((a, b) => a.sortOrder - b.sortOrder)
            .map((plan) => (
              <Card key={plan.id} className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h3 className="text-lg font-semibold text-gray-900">
                        {plan.displayName}
                      </h3>
                      <PlanBadge
                        badgeType={plan.badgeType}
                        badgeColor={plan.badgeColor}
                        customBadgeUrl={plan.customBadgeUrl}
                        size="sm"
                      />
                      <Badge
                        variant={plan.isActive ? "success" : "secondary"}
                        className="text-xs"
                      >
                        {plan.isActive ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                    <p className="mt-1 text-sm text-gray-500">{plan.name}</p>
                    <p className="mt-2 text-xl font-bold text-blue-600">
                      {formatPrice(plan.price, plan.currency, plan.billingCycle)}
                    </p>
                    {plan.description && (
                      <p className="mt-2 text-sm text-gray-600 line-clamp-2">
                        {plan.description}
                      </p>
                    )}
                    <p className="mt-2 text-xs text-gray-500">
                      Features: {formatFeatures(plan.features)}
                    </p>
                  </div>
                  <div className="flex flex-col space-y-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => updateSortOrder(plan.id, 'up')}
                      className="p-1"
                    >
                      <ArrowUpIcon className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => updateSortOrder(plan.id, 'down')}
                      className="p-1"
                    >
                      <ArrowDownIcon className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div className="mt-4 flex items-center justify-between">
                  <div className="flex space-x-2">
                    <Link href={`/admin/subscriptions/plans/${plan.id}`}>
                      <Button variant="ghost" size="sm">
                        <EyeIcon className="mr-1 h-4 w-4" />
                        View
                      </Button>
                    </Link>
                    <Link href={`/admin/subscriptions/plans/${plan.id}/edit`}>
                      <Button variant="ghost" size="sm">
                        <PencilIcon className="mr-1 h-4 w-4" />
                        Edit
                      </Button>
                    </Link>
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => togglePlanStatus(plan.id, plan.isActive)}
                      className={plan.isActive ? "text-red-600 hover:text-red-700" : "text-green-600 hover:text-green-700"}
                    >
                      {plan.isActive ? (
                        <XCircleIcon className="mr-1 h-4 w-4" />
                      ) : (
                        <CheckCircleIcon className="mr-1 h-4 w-4" />
                      )}
                      {plan.isActive ? "Deactivate" : "Activate"}
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => deletePlan(plan.id)}
                      disabled={isDeleting === plan.id}
                      className="text-red-600 hover:text-red-700"
                    >
                      {isDeleting === plan.id ? (
                        <Spinner size="sm" />
                      ) : (
                        <TrashIcon className="mr-1 h-4 w-4" />
                      )}
                      Delete
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
        </div>
      )}
    </AdminLayout>
  );
}
