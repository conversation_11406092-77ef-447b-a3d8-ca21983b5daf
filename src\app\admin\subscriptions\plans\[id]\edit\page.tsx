"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { AdminLayout } from "@/components/admin/AdminLayout";
import { Button } from "@/components/ui/Button";
import { Card } from "@/components/ui/Card";
import { Spinner } from "@/components/ui/Spinner";
import { PlanBadge, BADGE_TYPE_OPTIONS, BADGE_COLOR_OPTIONS } from "@/components/ui/PlanBadge";
import { toast } from "react-hot-toast";
import {
  ArrowLeftIcon,
  PlusIcon,
  TrashIcon,
} from "@heroicons/react/24/outline";
import Link from "next/link";

interface SubscriptionPlan {
  id: string;
  name: string;
  displayName: string;
  description: string | null;
  price: string;
  currency: string;
  billingCycle: 'monthly' | 'yearly';
  features: string[];
  maxPosts: number;
  maxStorage: number;
  maxGroups: number;
  canCreateFanPages: boolean;
  canCreateStores: boolean;
  canMonetizeBlogs: boolean;
  prioritySupport: boolean;
  grantsVerification: boolean;
  // Badge system fields
  badgeType: 'none' | 'crown' | 'star' | 'diamond' | 'vip' | 'custom';
  badgeColor: string;
  customBadgeUrl: string | null;
  badgePriority: number;
  isActive: boolean;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
}

export default function EditSubscriptionPlanPage() {
  const params = useParams();
  const router = useRouter();
  const planId = params.id as string;
  
  const [plan, setPlan] = useState<SubscriptionPlan | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    displayName: '',
    description: '',
    price: '',
    currency: 'USD',
    billingCycle: 'monthly' as 'monthly' | 'yearly',
    features: [] as string[],
    maxPosts: -1,
    maxStorage: -1,
    maxGroups: -1,
    canCreateFanPages: false,
    canCreateStores: false,
    canMonetizeBlogs: false,
    prioritySupport: false,
    badgeType: 'none' as 'none' | 'crown' | 'star' | 'diamond' | 'vip' | 'custom',
    badgeColor: '#3B82F6',
    customBadgeUrl: '',
    badgePriority: 0,
    isActive: true,
    sortOrder: 0,
  });
  const [newFeature, setNewFeature] = useState('');

  useEffect(() => {
    if (planId) {
      fetchPlan();
    }
  }, [planId]);

  const fetchPlan = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/admin/subscriptions/plans/${planId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch subscription plan');
      }
      const data = await response.json();
      setPlan(data.plan);
      
      // Set form data
      setFormData({
        name: data.plan.name,
        displayName: data.plan.displayName,
        description: data.plan.description || '',
        price: data.plan.price,
        currency: data.plan.currency,
        billingCycle: data.plan.billingCycle,
        features: data.plan.features || [],
        maxPosts: data.plan.maxPosts,
        maxStorage: data.plan.maxStorage,
        maxGroups: data.plan.maxGroups,
        canCreateFanPages: data.plan.canCreateFanPages,
        canCreateStores: data.plan.canCreateStores,
        canMonetizeBlogs: data.plan.canMonetizeBlogs,
        prioritySupport: data.plan.prioritySupport,
        grantsVerification: data.plan.grantsVerification || false,
        badgeType: data.plan.badgeType,
        badgeColor: data.plan.badgeColor,
        customBadgeUrl: data.plan.customBadgeUrl || '',
        badgePriority: data.plan.badgePriority,
        isActive: data.plan.isActive,
        sortOrder: data.plan.sortOrder,
      });
    } catch (error) {
      console.error('Error fetching plan:', error);
      toast.error('Failed to load subscription plan');
      router.push('/admin/subscriptions/plans');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setIsSaving(true);
      
      const response = await fetch(`/api/admin/subscriptions/plans/${planId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update subscription plan');
      }

      toast.success('Subscription plan updated successfully');
      router.push(`/admin/subscriptions/plans/${planId}`);
    } catch (error) {
      console.error('Error updating plan:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to update subscription plan');
    } finally {
      setIsSaving(false);
    }
  };

  const addFeature = () => {
    if (newFeature.trim()) {
      setFormData(prev => ({
        ...prev,
        features: [...prev.features, newFeature.trim()]
      }));
      setNewFeature('');
    }
  };

  const removeFeature = (index: number) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.filter((_, i) => i !== index)
    }));
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <Spinner size="lg" />
        </div>
      </AdminLayout>
    );
  }

  if (!plan) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900">Plan not found</h2>
          <p className="mt-2 text-gray-600">The subscription plan you're looking for doesn't exist.</p>
          <Link href="/admin/subscriptions/plans" className="mt-4 inline-block">
            <Button>
              <ArrowLeftIcon className="mr-2 h-5 w-5" />
              Back to Plans
            </Button>
          </Link>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="mb-6">
        <div className="flex items-center space-x-4 mb-4">
          <Link href={`/admin/subscriptions/plans/${planId}`}>
            <Button variant="ghost" size="sm">
              <ArrowLeftIcon className="mr-2 h-4 w-4" />
              Back to Plan
            </Button>
          </Link>
        </div>
        
        <div className="flex items-center space-x-3">
          <h1 className="text-2xl font-bold text-gray-900">Edit Plan: {plan.displayName}</h1>
          <PlanBadge
            badgeType={formData.badgeType}
            badgeColor={formData.badgeColor}
            customBadgeUrl={formData.customBadgeUrl || null}
            size="md"
          />
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <Card className="p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Plan Name *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Display Name *
                  </label>
                  <input
                    type="text"
                    value={formData.displayName}
                    onChange={(e) => handleInputChange('displayName', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Price *
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={formData.price}
                    onChange={(e) => handleInputChange('price', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Currency
                  </label>
                  <select
                    value={formData.currency}
                    onChange={(e) => handleInputChange('currency', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="USD">USD</option>
                    <option value="EUR">EUR</option>
                    <option value="GBP">GBP</option>
                    <option value="BDT">BDT</option>
                  </select>
                </div>
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Billing Cycle
                  </label>
                  <select
                    value={formData.billingCycle}
                    onChange={(e) => handleInputChange('billingCycle', e.target.value as 'monthly' | 'yearly')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="monthly">Monthly</option>
                    <option value="yearly">Yearly</option>
                  </select>
                </div>
              </div>
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Plan description..."
                />
              </div>
            </Card>

            {/* Features */}
            <Card className="p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Features</h2>
              <div className="space-y-3">
                {formData.features.map((feature, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <input
                      type="text"
                      value={feature}
                      onChange={(e) => {
                        const newFeatures = [...formData.features];
                        newFeatures[index] = e.target.value;
                        handleInputChange('features', newFeatures);
                      }}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFeature(index)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                <div className="flex items-center space-x-2">
                  <input
                    type="text"
                    value={newFeature}
                    onChange={(e) => setNewFeature(e.target.value)}
                    placeholder="Add new feature..."
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        addFeature();
                      }
                    }}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={addFeature}
                  >
                    <PlusIcon className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Limits */}
            <Card className="p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Limits</h2>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Max Posts (-1 for unlimited)
                  </label>
                  <input
                    type="number"
                    value={formData.maxPosts}
                    onChange={(e) => handleInputChange('maxPosts', isNaN(parseInt(e.target.value)) ? -1 : parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Max Storage MB (-1 for unlimited)
                  </label>
                  <input
                    type="number"
                    value={formData.maxStorage}
                    onChange={(e) => handleInputChange('maxStorage', isNaN(parseInt(e.target.value)) ? -1 : parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Max Groups (-1 for unlimited)
                  </label>
                  <input
                    type="number"
                    value={formData.maxGroups}
                    onChange={(e) => handleInputChange('maxGroups', isNaN(parseInt(e.target.value)) ? -1 : parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
            </Card>

            {/* Permissions */}
            <Card className="p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Permissions</h2>
              <div className="space-y-3">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.canCreateFanPages}
                    onChange={(e) => handleInputChange('canCreateFanPages', e.target.checked)}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700">Can Create Fan Pages</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.canCreateStores}
                    onChange={(e) => handleInputChange('canCreateStores', e.target.checked)}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700">Can Create Stores</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.canMonetizeBlogs}
                    onChange={(e) => handleInputChange('canMonetizeBlogs', e.target.checked)}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700">Can Monetize Blogs</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.prioritySupport}
                    onChange={(e) => handleInputChange('prioritySupport', e.target.checked)}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700">Priority Support</span>
                </label>
                <div>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.grantsVerification}
                      onChange={(e) => handleInputChange('grantsVerification', e.target.checked)}
                      className="mr-2"
                    />
                    <span className="text-sm text-gray-700">Grants User Verification</span>
                  </label>
                  <p className="text-xs text-gray-500 mt-1 ml-6">
                    Subscribers will automatically get a verified badge
                  </p>
                </div>
              </div>
            </Card>

            {/* Badge Settings */}
            <Card className="p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Badge Settings</h2>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Badge Type
                  </label>
                  <select
                    value={formData.badgeType}
                    onChange={(e) => handleInputChange('badgeType', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {BADGE_TYPE_OPTIONS.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
                
                {formData.badgeType !== 'none' && (
                  <>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Badge Color
                      </label>
                      <div className="flex space-x-2">
                        <input
                          type="color"
                          value={formData.badgeColor}
                          onChange={(e) => handleInputChange('badgeColor', e.target.value)}
                          className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                        />
                        <select
                          value={formData.badgeColor}
                          onChange={(e) => handleInputChange('badgeColor', e.target.value)}
                          className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          {BADGE_COLOR_OPTIONS.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Badge Priority (0-100)
                      </label>
                      <input
                        type="number"
                        min="0"
                        max="100"
                        value={formData.badgePriority}
                        onChange={(e) => handleInputChange('badgePriority', parseInt(e.target.value) || 0)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                    
                    {formData.badgeType === 'custom' && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Custom Badge URL
                        </label>
                        <input
                          type="url"
                          value={formData.customBadgeUrl}
                          onChange={(e) => handleInputChange('customBadgeUrl', e.target.value)}
                          placeholder="https://example.com/badge.png"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                    )}
                  </>
                )}
              </div>
            </Card>

            {/* Settings */}
            <Card className="p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Settings</h2>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Sort Order
                  </label>
                  <input
                    type="number"
                    value={formData.sortOrder}
                    onChange={(e) => handleInputChange('sortOrder', parseInt(e.target.value) || 0)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.isActive}
                    onChange={(e) => handleInputChange('isActive', e.target.checked)}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700">Active</span>
                </label>
              </div>
            </Card>
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
          <Link href={`/admin/subscriptions/plans/${planId}`}>
            <Button type="button" variant="outline">
              Cancel
            </Button>
          </Link>
          <Button type="submit" disabled={isSaving}>
            {isSaving ? (
              <>
                <Spinner size="sm" className="mr-2" />
                Saving...
              </>
            ) : (
              'Save Changes'
            )}
          </Button>
        </div>
      </form>
    </AdminLayout>
  );
}
