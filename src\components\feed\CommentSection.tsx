"use client";

import { useState, useEffect, useCallback } from "react";
import { useSession } from "next-auth/react";
import Image from "next/image";
import Link from "next/link";
import { formatTimeAgo } from "@/lib/utils";
import { Button } from "@/components/ui/Button";
import { Spinner } from "@/components/ui/Spinner";
import { CommentTime } from "@/components/ui/TimeDisplay";
import eventBus from "@/lib/eventBus";
import { renderTextWithMentions } from "@/lib/utils/mention-utils";
import { CommentActions } from "./CommentActions";
import { UserWithBadge } from "@/components/ui/UserWithBadge";

interface Comment {
  id: string;
  content: string;
  createdAt: string;
  parentId?: string | null;
  user: {
    id: string;
    name: string;
    username?: string | null;
    image: string | null;
  };
  _count?: {
    likes: number;
    dislikes: number;
    replies: number;
  };
  liked?: boolean;
  disliked?: boolean;
  replies?: Comment[];
}

interface CommentSectionProps {
  postId: string;
  postType?: 'user_post' | 'fan_page_post' | 'group_post' | 'blog_post';
  blogSlug?: string; // Required for blog posts
}

export function CommentSection({ postId, postType = 'user_post', blogSlug }: CommentSectionProps) {
  const { data: session } = useSession();
  const [comments, setComments] = useState<Comment[]>([]);
  const [newComment, setNewComment] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyContent, setReplyContent] = useState("");
  const [replyingToUser, setReplyingToUser] = useState<string | null>(null);
  const [expandedReplies, setExpandedReplies] = useState<Set<string>>(new Set());
  const [editingComment, setEditingComment] = useState<string | null>(null);
  const [editContent, setEditContent] = useState("");
  const [isUpdating, setIsUpdating] = useState(false);

  // Create a memoized fetchComments function that can be reused
  const fetchComments = useCallback(async () => {
    try {
      setIsLoading(true);

      // Use different API endpoints based on post type
      let apiUrl: string;
      if (postType === 'blog_post' && blogSlug) {
        apiUrl = `/api/blogs/${blogSlug}/comments`;
      } else {
        apiUrl = `/api/posts/${postId}/comments`;
      }

      const response = await fetch(apiUrl);
      if (!response.ok) throw new Error("Failed to fetch comments");
      const data = await response.json();

      // Comments are already organized by the API
      setComments(data);
    } catch (error) {
      console.error("Error fetching comments:", error);
    } finally {
      setIsLoading(false);
    }
  }, [postId, postType, blogSlug]);

  // Initial fetch when component mounts
  useEffect(() => {
    fetchComments();
  }, [fetchComments]);

  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newComment.trim() || !session?.user) return;

    setIsSubmitting(true);

    try {
      // Use different API endpoints based on post type
      let apiUrl: string;
      if (postType === 'blog_post' && blogSlug) {
        apiUrl = `/api/blogs/${blogSlug}/comments`;
      } else {
        apiUrl = `/api/posts/${postId}/comments`;
      }

      const response = await fetch(apiUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ content: newComment }),
      });

      if (!response.ok) throw new Error("Failed to post comment");

      // Refresh comments
      fetchComments();

      // Emit event to notify that a comment was added
      eventBus.emit('comment-added', postId);

      setNewComment("");
    } catch (error) {
      console.error("Error posting comment:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSubmitReply = async (e: React.FormEvent, parentId: string) => {
    e.preventDefault();

    if (!replyContent.trim() || !session?.user) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Use different API endpoints based on post type
      let apiUrl: string;
      if (postType === 'blog_post' && blogSlug) {
        apiUrl = `/api/blogs/${blogSlug}/comments`;
      } else {
        apiUrl = `/api/posts/${postId}/comments`;
      }

      const response = await fetch(apiUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          content: replyContent,
          parentId: parentId,
          mentionedUserId: replyingToUser // Send mentioned user ID
        }),
      });

      if (!response.ok) throw new Error("Failed to post reply");

      // Refresh comments to get updated structure
      fetchComments();

      // Emit event to notify that a comment was added
      eventBus.emit('comment-added', postId);

      setReplyContent("");
      setReplyingTo(null);
      setReplyingToUser(null);
    } catch (error) {
      console.error("Error posting reply:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const toggleReplies = (commentId: string) => {
    setExpandedReplies(prev => {
      const newSet = new Set(prev);
      if (newSet.has(commentId)) {
        newSet.delete(commentId);
      } else {
        newSet.add(commentId);
      }
      return newSet;
    });
  };

  const handleEditComment = (comment: Comment) => {
    setEditingComment(comment.id);
    setEditContent(comment.content);
  };

  const handleCancelEdit = () => {
    setEditingComment(null);
    setEditContent("");
  };

  const handleSaveEdit = async () => {
    if (!editContent.trim() || !editingComment) return;

    setIsUpdating(true);
    try {
      const response = await fetch(`/api/comments/${editingComment}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ content: editContent.trim() }),
      });

      if (!response.ok) throw new Error("Failed to update comment");

      // Refresh comments to get updated data
      fetchComments();

      setEditingComment(null);
      setEditContent("");
    } catch (error) {
      console.error("Error updating comment:", error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleDeleteComment = async (commentId: string) => {
    try {
      const response = await fetch(`/api/comments/${commentId}`, {
        method: "DELETE",
      });

      if (!response.ok) throw new Error("Failed to delete comment");

      // Refresh comments to get updated list
      fetchComments();
    } catch (error) {
      console.error("Error deleting comment:", error);
      throw error; // Re-throw to handle in CommentActions
    }
  };

  const handleLikeComment = async (commentId: string) => {
    try {
      // Use different API endpoints based on post type
      let apiUrl: string;
      if (postType === 'blog_post' && blogSlug) {
        apiUrl = `/api/blogs/${blogSlug}/comments/${commentId}/like`;
      } else {
        apiUrl = `/api/comments/${commentId}/like`;
      }

      const response = await fetch(apiUrl, {
        method: "POST"
      });

      if (!response.ok) {
        throw new Error("Failed to like comment");
      }

      // Update the state optimistically
      updateCommentInState(commentId, (comment) => {
        const newLiked = !(comment.liked || false);
        const wasDisliked = comment.disliked || false;

        return {
          ...comment,
          liked: newLiked,
          disliked: wasDisliked ? false : comment.disliked,
          _count: {
            ...(comment._count || { likes: 0, dislikes: 0, replies: 0 }),
            likes: (comment._count?.likes || 0) + (newLiked ? 1 : -1),
            dislikes: wasDisliked ? (comment._count?.dislikes || 0) - 1 : (comment._count?.dislikes || 0),
          },
        };
      });
    } catch (error) {
      console.error("Error liking comment:", error);
    }
  };

  const handleDislikeComment = async (commentId: string) => {
    try {
      // Use different API endpoints based on post type
      let apiUrl: string;
      if (postType === 'blog_post' && blogSlug) {
        apiUrl = `/api/blogs/${blogSlug}/comments/${commentId}/dislike`;
      } else {
        apiUrl = `/api/comments/${commentId}/dislike`;
      }

      const response = await fetch(apiUrl, {
        method: "POST"
      });

      if (!response.ok) {
        throw new Error("Failed to dislike comment");
      }

      // Update the state optimistically
      updateCommentInState(commentId, (comment) => {
        const newDisliked = !(comment.disliked || false);
        const wasLiked = comment.liked || false;

        return {
          ...comment,
          disliked: newDisliked,
          liked: wasLiked ? false : comment.liked,
          _count: {
            ...(comment._count || { likes: 0, dislikes: 0, replies: 0 }),
            dislikes: (comment._count?.dislikes || 0) + (newDisliked ? 1 : -1),
            likes: wasLiked ? (comment._count?.likes || 0) - 1 : (comment._count?.likes || 0),
          },
        };
      });
    } catch (error) {
      console.error("Error disliking comment:", error);
    }
  };

  // Helper function to update comment in nested structure
  const updateCommentInState = (commentId: string, updateFn: (comment: Comment) => Comment) => {
    const updateCommentsRecursively = (comments: Comment[]): Comment[] => {
      return comments.map(comment => {
        if (comment.id === commentId) {
          return updateFn(comment);
        }
        if (comment.replies && comment.replies.length > 0) {
          return {
            ...comment,
            replies: updateCommentsRecursively(comment.replies)
          };
        }
        return comment;
      });
    };

    setComments(prevComments => updateCommentsRecursively(prevComments));
  };

  return (
    <div className="mt-3 pt-2 border-t border-gray-100">
      {isLoading ? (
        <div className="flex justify-center py-2">
          <Spinner size="sm" />
        </div>
      ) : null}

      {/* Comments list */}
      <div className="space-y-3 mb-3">
        {comments.length === 0 && !isLoading ? (
          <p className="text-center text-xs text-gray-500 py-2">
            No comments yet. Be the first to comment!
          </p>
        ) : (
          comments.map((comment) => (
            <div key={comment.id} className="space-y-2">
              {/* Main Comment */}
              <div className="flex space-x-2">
                <div className="h-8 w-8 flex-shrink-0 rounded-full bg-gray-200 overflow-hidden">
                  {comment.user.image ? (
                    <Image
                      src={comment.user.image}
                      alt={comment.user.name}
                      width={32}
                      height={32}
                      className="rounded-full object-cover"
                    />
                  ) : (
                    <div className="h-full w-full flex items-center justify-center bg-gray-300 text-gray-600 text-sm font-bold">
                      {comment.user.name?.charAt(0).toUpperCase() || "U"}
                    </div>
                  )}
                </div>
                <div className="flex-1">
                  {editingComment === comment.id ? (
                    // Edit mode
                    <div className="space-y-2">
                      <div className="rounded-2xl bg-gray-100 px-3 py-2">
                        <UserWithBadge
                          user={comment.user}
                          linkToProfile={true}
                          badgeSize="sm"
                          nameClassName="text-xs font-medium text-gray-900 hover:text-blue-600 hover:underline cursor-pointer"
                        />
                        <textarea
                          value={editContent}
                          onChange={(e) => setEditContent(e.target.value)}
                          className="w-full mt-1 text-sm text-gray-700 bg-transparent border-none resize-none focus:outline-none"
                          rows={2}
                          maxLength={1000}
                          disabled={isUpdating}
                        />
                      </div>
                      <div className="flex items-center space-x-2 pl-2">
                        <button
                          onClick={handleSaveEdit}
                          disabled={isUpdating || !editContent.trim()}
                          className="text-xs bg-blue-600 text-white px-3 py-1 rounded-full hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {isUpdating ? "Saving..." : "Save"}
                        </button>
                        <button
                          onClick={handleCancelEdit}
                          disabled={isUpdating}
                          className="text-xs text-gray-500 hover:text-gray-700 disabled:opacity-50"
                        >
                          Cancel
                        </button>
                      </div>
                    </div>
                  ) : (
                    // View mode
                    <div className="rounded-2xl bg-gray-100 px-3 py-2 relative group">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <UserWithBadge
                            user={comment.user}
                            linkToProfile={true}
                            badgeSize="sm"
                            nameClassName="text-xs font-medium text-gray-900 hover:text-blue-600 hover:underline cursor-pointer"
                          />
                          <p className="text-sm text-gray-700">
                            {renderTextWithMentions(comment.content)}
                          </p>
                        </div>
                        <CommentActions
                          commentId={comment.id}
                          commentUserId={comment.user.id}
                          onEdit={() => handleEditComment(comment)}
                          onDelete={() => handleDeleteComment(comment.id)}
                          className="opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                          size="sm"
                        />
                      </div>
                    </div>
                  )}
                  <div className="mt-1 flex items-center space-x-3 pl-2 text-xs">
                    <button
                      className={`${
                        comment.liked || false
                          ? "text-blue-600 font-medium"
                          : "text-gray-500 hover:text-blue-600"
                      }`}
                      onClick={() => handleLikeComment(comment.id)}
                    >
                      Like{(comment._count?.likes || 0) > 0 && ` ${comment._count?.likes || 0}`}
                    </button>
                    <button
                      className={`${
                        comment.disliked || false
                          ? "text-red-600 font-medium"
                          : "text-gray-500 hover:text-red-600"
                      }`}
                      onClick={() => handleDislikeComment(comment.id)}
                    >
                      Dislike{(comment._count?.dislikes || 0) > 0 && ` ${comment._count?.dislikes || 0}`}
                    </button>
                    <button
                      className="text-gray-500 hover:text-blue-600"
                      onClick={() => {
                        if (replyingTo === comment.id) {
                          setReplyingTo(null);
                          setReplyContent("");
                          setReplyingToUser(null);
                        } else {
                          setReplyingTo(comment.id);
                          const username = comment.user.username || comment.user.name;
                          setReplyContent(`@${username} `);
                          setReplyingToUser(comment.user.id);
                        }
                      }}
                    >
                      Reply
                    </button>
                    <CommentTime
                      date={comment.createdAt}
                      className="text-gray-400 text-xs"
                      autoUpdate={true}
                    />
                  </div>
                </div>
              </div>

              {/* Reply Form */}
              {replyingTo === comment.id && (
                <div className="ml-10 mt-2">
                  <form onSubmit={(e) => handleSubmitReply(e, comment.id)} className="flex items-center space-x-2">
                    <div className="h-6 w-6 flex-shrink-0 rounded-full bg-gray-200 overflow-hidden">
                      {session?.user?.image ? (
                        <Image
                          src={session.user.image}
                          alt={session.user.name || "User"}
                          width={24}
                          height={24}
                          className="rounded-full object-cover"
                        />
                      ) : (
                        <div className="h-full w-full flex items-center justify-center bg-gray-300 text-gray-600 text-xs font-bold">
                          {session?.user?.name?.charAt(0).toUpperCase() || "U"}
                        </div>
                      )}
                    </div>
                    <div className="flex-1 relative">
                      <input
                        type="text"
                        placeholder={`Reply to ${comment.user.name}...`}
                        className="w-full rounded-full border border-gray-300 bg-gray-100 px-3 py-1 text-xs focus:border-blue-500 focus:bg-white focus:outline-none"
                        value={replyContent}
                        onChange={(e) => setReplyContent(e.target.value)}
                        autoFocus
                      />
                      {isSubmitting && (
                        <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                          <Spinner size="sm" />
                        </div>
                      )}
                    </div>
                    <button
                      type="button"
                      onClick={() => setReplyingTo(null)}
                      className="text-gray-400 hover:text-gray-600 text-xs"
                    >
                      Cancel
                    </button>
                  </form>
                </div>
              )}

              {/* Replies */}
              {comment.replies && comment.replies.length > 0 && (
                <div className="ml-10 space-y-2">
                  <div className="text-xs text-gray-600 font-medium mb-2">
                    {comment.replies.length} {comment.replies.length === 1 ? 'reply' : 'replies'}:
                  </div>
                  {comment.replies.map((reply) => (
                        <div key={reply.id} className="flex space-x-2">
                          <div className="h-6 w-6 flex-shrink-0 rounded-full bg-gray-200 overflow-hidden">
                            {reply.user.image ? (
                              <Image
                                src={reply.user.image}
                                alt={reply.user.name}
                                width={24}
                                height={24}
                                className="rounded-full object-cover"
                              />
                            ) : (
                              <div className="h-full w-full flex items-center justify-center bg-gray-300 text-gray-600 text-xs font-bold">
                                {reply.user.name?.charAt(0).toUpperCase() || "U"}
                              </div>
                            )}
                          </div>
                          <div className="flex-1">
                            {editingComment === reply.id ? (
                              // Edit mode for reply
                              <div className="space-y-2">
                                <div className="rounded-xl bg-gray-50 px-2 py-1">
                                  <Link
                                    href={`/user/${reply.user.username || reply.user.id}`}
                                    className="text-xs font-medium text-gray-900 hover:text-blue-600 hover:underline cursor-pointer"
                                  >
                                    {reply.user.name}
                                  </Link>
                                  <textarea
                                    value={editContent}
                                    onChange={(e) => setEditContent(e.target.value)}
                                    className="w-full mt-1 text-xs text-gray-700 bg-transparent border-none resize-none focus:outline-none"
                                    rows={2}
                                    maxLength={1000}
                                    disabled={isUpdating}
                                  />
                                </div>
                                <div className="flex items-center space-x-2 pl-1">
                                  <button
                                    onClick={handleSaveEdit}
                                    disabled={isUpdating || !editContent.trim()}
                                    className="text-xs bg-blue-600 text-white px-2 py-1 rounded-full hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                                  >
                                    {isUpdating ? "Saving..." : "Save"}
                                  </button>
                                  <button
                                    onClick={handleCancelEdit}
                                    disabled={isUpdating}
                                    className="text-xs text-gray-500 hover:text-gray-700 disabled:opacity-50"
                                  >
                                    Cancel
                                  </button>
                                </div>
                              </div>
                            ) : (
                              // View mode for reply
                              <div className="rounded-xl bg-gray-50 px-2 py-1 relative group">
                                <div className="flex items-start justify-between">
                                  <div className="flex-1">
                                    <Link
                                      href={`/user/${reply.user.username || reply.user.id}`}
                                      className="text-xs font-medium text-gray-900 hover:text-blue-600 hover:underline cursor-pointer"
                                    >
                                      {reply.user.name}
                                    </Link>
                                    <p className="text-xs text-gray-700">
                                      {renderTextWithMentions(reply.content)}
                                    </p>
                                  </div>
                                  <CommentActions
                                    commentId={reply.id}
                                    commentUserId={reply.user.id}
                                    onEdit={() => handleEditComment(reply)}
                                    onDelete={() => handleDeleteComment(reply.id)}
                                    className="opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                                    size="sm"
                                  />
                                </div>
                              </div>
                            )}
                            <div className="mt-1 flex items-center space-x-2 pl-1 text-xs">
                              <button
                                className={`${
                                  reply.liked || false
                                    ? "text-blue-600 font-medium"
                                    : "text-gray-500 hover:text-blue-600"
                                }`}
                                onClick={() => handleLikeComment(reply.id)}
                              >
                                Like{(reply._count?.likes || 0) > 0 && ` ${reply._count?.likes || 0}`}
                              </button>
                              <button
                                className={`${
                                  reply.disliked || false
                                    ? "text-red-600 font-medium"
                                    : "text-gray-500 hover:text-red-600"
                                }`}
                                onClick={() => handleDislikeComment(reply.id)}
                              >
                                Dislike{(reply._count?.dislikes || 0) > 0 && ` ${reply._count?.dislikes || 0}`}
                              </button>
                              <CommentTime
                                date={reply.createdAt}
                                className="text-gray-400 text-xs"
                                autoUpdate={true}
                              />
                            </div>
                          </div>
                        </div>
                      ))}
                </div>
              )}
            </div>
          ))
        )}
      </div>

      {/* Comment form */}
      <form onSubmit={handleSubmitComment} className="flex items-center space-x-2">
        <div className="h-8 w-8 flex-shrink-0 rounded-full bg-gray-200 overflow-hidden">
          {session?.user?.image ? (
            <Image
              src={session.user.image}
              alt={session.user.name || "User"}
              width={32}
              height={32}
              className="rounded-full object-cover"
            />
          ) : (
            <div className="h-full w-full flex items-center justify-center bg-gray-300 text-gray-600 text-sm font-bold">
              {session?.user?.name?.charAt(0).toUpperCase() || "U"}
            </div>
          )}
        </div>
        <div className="flex-1 relative">
          <input
            type="text"
            placeholder="Write a comment..."
            className="w-full rounded-full border border-gray-300 bg-gray-100 px-3 py-1.5 text-sm focus:border-blue-500 focus:bg-white focus:outline-none"
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
          />
          {isSubmitting && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <Spinner size="sm" />
            </div>
          )}
        </div>
      </form>
    </div>
  );
}
